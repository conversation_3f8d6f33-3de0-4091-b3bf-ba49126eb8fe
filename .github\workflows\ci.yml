name: CI

on:
  push:
    branches-ignore:
      - main
    paths:
      # Run this pipeline only if there are changes in specified path
      - 'apps/**'
      - 'services/**'
      - 'examples/**'
      - 'experiments/**'
  workflow_call:
  workflow_dispatch:

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: <PERSON><PERSON>
        uses: ./.github/actions/lint-action

  format:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Format
        uses: ./.github/actions/format-action

  build:
    strategy:
      matrix:
        os: [ubuntu-latest]
    runs-on: ${{ matrix.os }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Build
        uses: ./.github/actions/build-action

  spell_check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Spellcheck
        uses: ./.github/actions/spell-check-action

  test_linux:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Test
        uses: ./.github/actions/test-action
