global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'bullmq-metrics'
    static_configs:
      - targets: ['host.docker.internal:3000']
    metrics_path: /api/v1/metrics

  - job_name: 'workflows-service'
    static_configs:
      - targets: ['localhost:3000']
    metrics_path: /api/metrics/prometheus
    
  - job_name: 'credit-scoring-service'
    static_configs:
      - targets: ['credit-scoring-service:5000']
    metrics_path: /metrics