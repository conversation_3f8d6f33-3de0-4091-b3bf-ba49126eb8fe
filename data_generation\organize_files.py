"""
Script pour organiser et expliquer la structure des fichiers générés
"""

import os
import pandas as pd

def analyze_file_structure():
    """Analyse et explique la structure des fichiers"""
    
    print("=" * 70)
    print("ORGANISATION DES FICHIERS DATASET MULTICATÉGORIES")
    print("=" * 70)
    
    output_dir = "output"
    files = [f for f in os.listdir(output_dir) if f.endswith('.csv')]
    
    # Catégoriser les fichiers
    fichiers_principaux = []
    fichiers_categories = []
    fichiers_tre = []
    fichiers_ert = []
    fichiers_classes = []
    fichiers_test = []
    
    for file in files:
        if 'multicategories' in file:
            fichiers_principaux.append(file)
        elif 'tre_' in file:
            fichiers_tre.append(file)
        elif 'ert_' in file:
            fichiers_ert.append(file)
        elif 'tunisien_resident_etranger' in file or 'etranger_resident_tunisie' in file or 'tunisien_resident.csv' in file:
            fichiers_categories.append(file)
        elif 'classe_' in file:
            fichiers_classes.append(file)
        elif 'test' in file or '1k' in file or '10k' in file:
            fichiers_test.append(file)
    
    print("📁 FICHIERS PRINCIPAUX (Dataset Complet)")
    print("-" * 50)
    for file in fichiers_principaux:
        filepath = os.path.join(output_dir, file)
        size_mb = os.path.getsize(filepath) / 1024**2
        df = pd.read_csv(filepath)
        print(f"  📄 {file}")
        print(f"      Clients: {len(df):,} | Colonnes: {len(df.columns)} | Taille: {size_mb:.1f} MB")
        print(f"      Usage: Dataset principal avec toutes les catégories")
    
    print(f"\n📂 FICHIERS PAR CATÉGORIE (3 fichiers)")
    print("-" * 50)
    for file in sorted(fichiers_categories):
        filepath = os.path.join(output_dir, file)
        size_mb = os.path.getsize(filepath) / 1024**2
        df = pd.read_csv(filepath)
        
        if 'tunisien_resident.csv' in file:
            description = "Tunisiens résidents en Tunisie"
        elif 'tunisien_resident_etranger' in file:
            description = "Tunisiens résidents à l'étranger (TRE)"
        elif 'etranger_resident_tunisie' in file:
            description = "Étrangers résidents en Tunisie (ERT)"
        else:
            description = "Catégorie spécifique"
            
        print(f"  📄 {file}")
        print(f"      Clients: {len(df):,} | Taille: {size_mb:.1f} MB")
        print(f"      Usage: {description}")
    
    print(f"\n🌍 FICHIERS TRE PAR PAYS ({len(fichiers_tre)} fichiers)")
    print("-" * 50)
    for file in sorted(fichiers_tre):
        filepath = os.path.join(output_dir, file)
        size_mb = os.path.getsize(filepath) / 1024**2
        df = pd.read_csv(filepath)
        pays = file.replace('tunisian_credit_tre_', '').replace('.csv', '')
        print(f"  📄 {file}")
        print(f"      Clients: {len(df):,} | Pays: {pays.capitalize()}")
        print(f"      Usage: Analyse spécifique TRE {pays}")
    
    print(f"\n🏛️ FICHIERS ERT PAR NATIONALITÉ ({len(fichiers_ert)} fichiers)")
    print("-" * 50)
    for file in sorted(fichiers_ert):
        filepath = os.path.join(output_dir, file)
        size_mb = os.path.getsize(filepath) / 1024**2
        df = pd.read_csv(filepath)
        nationalite = file.replace('tunisian_credit_ert_', '').replace('.csv', '')
        print(f"  📄 {file}")
        print(f"      Clients: {len(df):,} | Nationalité: {nationalite.capitalize()}")
        print(f"      Usage: Analyse spécifique ERT {nationalite}")
    
    if fichiers_classes:
        print(f"\n🏦 FICHIERS PAR CLASSE DE RISQUE ({len(fichiers_classes)} fichiers)")
        print("-" * 50)
        for file in sorted(fichiers_classes):
            filepath = os.path.join(output_dir, file)
            size_mb = os.path.getsize(filepath) / 1024**2
            df = pd.read_csv(filepath)
            classe = file.replace('tunisian_credit_classe_', '').replace('.csv', '')
            print(f"  📄 {file}")
            print(f"      Clients: {len(df):,} | Classe: {classe}")
            print(f"      Usage: Analyse risque {classe}")
    
    if fichiers_test:
        print(f"\n🧪 FICHIERS DE TEST ({len(fichiers_test)} fichiers)")
        print("-" * 50)
        for file in sorted(fichiers_test):
            filepath = os.path.join(output_dir, file)
            size_mb = os.path.getsize(filepath) / 1024**2
            df = pd.read_csv(filepath)
            print(f"  📄 {file}")
            print(f"      Clients: {len(df):,} | Taille: {size_mb:.1f} MB")
            print(f"      Usage: Tests et validation")
    
    # Recommandations d'usage
    print(f"\n💡 RECOMMANDATIONS D'USAGE")
    print("=" * 50)
    print("🎯 POUR L'ANALYSE GÉNÉRALE:")
    print("   → Utilisez: tunisian_credit_multicategories_100k.csv")
    print("   → Contient: Toutes les catégories (100k clients)")
    
    print(f"\n🎯 POUR L'ANALYSE PAR CATÉGORIE:")
    print("   → Tunisiens résidents: tunisian_credit_tunisien_resident.csv")
    print("   → TRE: tunisian_credit_tunisien_resident_etranger.csv")
    print("   → ERT: tunisian_credit_etranger_resident_tunisie.csv")
    
    print(f"\n🎯 POUR L'ANALYSE GÉOGRAPHIQUE:")
    print("   → TRE France: tunisian_credit_tre_france.csv")
    print("   → TRE USA: tunisian_credit_tre_usa.csv")
    print("   → ERT Française: tunisian_credit_ert_francaise.csv")
    print("   → etc...")
    
    print(f"\n🎯 POUR LA MODÉLISATION:")
    print("   → Modèle global: Dataset principal")
    print("   → Modèles spécialisés: Fichiers par catégorie")
    print("   → Validation géographique: Fichiers par pays/nationalité")
    
    # Calculer l'espace total
    total_size = sum(os.path.getsize(os.path.join(output_dir, f)) for f in files) / 1024**2
    print(f"\n📊 STATISTIQUES GLOBALES")
    print("-" * 30)
    print(f"Total fichiers CSV: {len(files)}")
    print(f"Espace total: {total_size:.1f} MB")
    print(f"Clients uniques: 100,000")
    print(f"Catégories: 3 (TR, TRE, ERT)")
    print(f"Pays TRE: 9")
    print(f"Nationalités ERT: 10")

if __name__ == "__main__":
    analyze_file_structure()
