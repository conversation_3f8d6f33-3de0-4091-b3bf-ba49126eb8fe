version: '3.8'

services:
  postgres:
    image: postgres:14
    container_name: ballerine-postgres
    environment:
      POSTGRES_DB: ballerine
      POSTGRES_USER: ballerine
      POSTGRES_PASSWORD: password
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "ballerine"]
      interval: 10s
      timeout: 5s
      retries: 5

  workflows-service:
    image: ghcr.io/ballerine-io/workflows-service:dev
    container_name: ballerine-workflows
    environment:
      DATABASE_URL: *******************************************/ballerine
      WORKFLOWS_PATH: ./workflows
      PORT: 3001
    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "3001:3001"

  credit-scoring-service:
    image: python:3.9-slim
    container_name: credit-scoring-service
    working_dir: /app
    volumes:
      - ./services/credit-scoring-service:/app
    command: >
      bash -c "pip install -r requirements.txt &&
              python app.py"
    environment:
      LOG_LEVEL: DEBUG
    ports:
      - "5001:5001"  # Changé de 5000 à 5001
    depends_on:
      - postgres
      - workflows-service

volumes:
  pgdata:


