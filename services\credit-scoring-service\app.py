from flask import Flask, request, jsonify
import logging

# Configuration du logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health_check():
    logger.info("Health check endpoint called")
    return jsonify({"status": "healthy"}), 200

@app.route('/api/score', methods=['POST'])
def score_application():
    logger.info("Score endpoint called")
    try:
        data = request.json
        logger.info(f"Received data: {data}")

        # Simuler un calcul de score simple
        score = 75

        # Déterminer le niveau de risque
        if score >= 80:
            risk_level = "low"
            decision = "approve"
        elif score >= 60:
            risk_level = "medium"
            decision = "manual_review"
        else:
            risk_level = "high"
            decision = "reject"

        result = {
            "score": score,
            "riskLevel": risk_level,
            "decision": decision
        }

        logger.info(f"Returning result: {result}")
        return jsonify(result), 200
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    logger.info("Starting Flask application")
    # Utiliser le port 5001 au lieu de 5000 (qui peut être utilisé par Windows)
    app.run(debug=True, host='0.0.0.0', port=5001)

