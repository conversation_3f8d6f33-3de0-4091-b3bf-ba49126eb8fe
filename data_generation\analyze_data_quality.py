"""
Analyse complète de la qualité des données pour identifier les problèmes
qui peuvent affecter négativement la modélisation
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_data_quality():
    """Analyse complète de la qualité des données"""
    
    print("=" * 80)
    print("ANALYSE QUALITÉ DONNÉES - DÉTECTION PROBLÈMES MODÉLISATION")
    print("=" * 80)
    
    # Charger le dataset
    df = pd.read_csv("output/tunisian_credit_multicategories_100k_clean.csv")
    print(f"📊 Dataset: {len(df):,} clients, {len(df.columns)} colonnes")
    
    # 1. ANALYSE DES COLONNES CONSTANTES (INUTILES)
    print(f"\n🔍 1. COLONNES CONSTANTES (INUTILES POUR MODÉLISATION)")
    print("-" * 60)
    
    colonnes_constantes = []
    for col in df.columns:
        if df[col].nunique() <= 1:
            valeur_unique = df[col].iloc[0] if len(df) > 0 else None
            print(f"❌ {col:<30}: Valeur unique = {valeur_unique}")
            colonnes_constantes.append(col)
    
    if not colonnes_constantes:
        print("✅ Aucune colonne constante détectée")
    
    # 2. ANALYSE COLONNES QUASI-CONSTANTES (>95% même valeur)
    print(f"\n🔍 2. COLONNES QUASI-CONSTANTES (>95% MÊME VALEUR)")
    print("-" * 60)
    
    colonnes_quasi_constantes = []
    for col in df.columns:
        if df[col].dtype in ['object', 'bool']:
            valeur_freq = df[col].value_counts()
            if len(valeur_freq) > 0:
                pct_max = valeur_freq.iloc[0] / len(df) * 100
                if pct_max > 95:
                    print(f"⚠️ {col:<30}: {pct_max:.1f}% = '{valeur_freq.index[0]}'")
                    colonnes_quasi_constantes.append(col)
    
    # 3. ANALYSE SPÉCIFIQUE default_flag ET situation_contentieux
    print(f"\n🔍 3. ANALYSE SPÉCIFIQUE default_flag & situation_contentieux")
    print("-" * 60)
    
    # default_flag
    if 'default_flag' in df.columns:
        default_counts = df['default_flag'].value_counts()
        print(f"default_flag distribution:")
        for val, count in default_counts.items():
            pct = count / len(df) * 100
            print(f"  {val}: {count:,} ({pct:.1f}%)")
        
        if default_counts.get(True, 0) == 0:
            print(f"❌ default_flag: Aucun client en défaut - COLONNE INUTILE")
    
    # situation_contentieux
    if 'situation_contentieux' in df.columns:
        contentieux_counts = df['situation_contentieux'].value_counts()
        print(f"\nsituation_contentieux distribution:")
        for val, count in contentieux_counts.items():
            pct = count / len(df) * 100
            print(f"  {val}: {count:,} ({pct:.1f}%)")
        
        if contentieux_counts.get(True, 0) == 0:
            print(f"❌ situation_contentieux: Aucun contentieux - COLONNE INUTILE")
    
    # 4. ANALYSE CORRÉLATIONS PARFAITES (REDONDANCE)
    print(f"\n🔍 4. CORRÉLATIONS PARFAITES (REDONDANCE)")
    print("-" * 60)
    
    # Sélectionner colonnes numériques
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    corr_matrix = df[numeric_cols].corr()
    
    # Trouver corrélations parfaites (>0.99 ou <-0.99)
    correlations_parfaites = []
    for i in range(len(corr_matrix.columns)):
        for j in range(i+1, len(corr_matrix.columns)):
            corr_val = corr_matrix.iloc[i, j]
            if abs(corr_val) > 0.99:
                col1 = corr_matrix.columns[i]
                col2 = corr_matrix.columns[j]
                correlations_parfaites.append((col1, col2, corr_val))
                print(f"⚠️ {col1} ↔ {col2}: r = {corr_val:.3f}")
    
    if not correlations_parfaites:
        print("✅ Aucune corrélation parfaite détectée")
    
    # 5. ANALYSE VALEURS ABERRANTES EXTRÊMES
    print(f"\n🔍 5. VALEURS ABERRANTES EXTRÊMES")
    print("-" * 60)
    
    colonnes_aberrantes = []
    for col in numeric_cols:
        if col in ['client_id', 'cin']:  # Ignorer les IDs
            continue
            
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 3 * IQR  # 3*IQR au lieu de 1.5 pour détecter les extrêmes
        upper_bound = Q3 + 3 * IQR
        
        aberrants = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
        if len(aberrants) > len(df) * 0.05:  # Plus de 5% d'aberrants
            pct_aberrants = len(aberrants) / len(df) * 100
            print(f"⚠️ {col:<30}: {len(aberrants):,} aberrants ({pct_aberrants:.1f}%)")
            print(f"   Plage normale: [{lower_bound:.1f}, {upper_bound:.1f}]")
            print(f"   Min-Max réels: [{df[col].min():.1f}, {df[col].max():.1f}]")
            colonnes_aberrantes.append(col)
    
    # 6. ANALYSE COHÉRENCE MÉTIER
    print(f"\n🔍 6. INCOHÉRENCES MÉTIER")
    print("-" * 60)
    
    incoherences = []
    
    # Âge vs ancienneté emploi
    if 'age' in df.columns and 'anciennete_emploi' in df.columns:
        age_travail_impossible = df[df['anciennete_emploi'] > (df['age'] - 16)]
        if len(age_travail_impossible) > 0:
            print(f"❌ Ancienneté emploi > âge-16: {len(age_travail_impossible):,} cas")
            incoherences.append('anciennete_emploi_vs_age')
    
    # Revenus négatifs ou nuls
    if 'revenu_mensuel' in df.columns:
        revenus_problematiques = df[df['revenu_mensuel'] <= 0]
        if len(revenus_problematiques) > 0:
            print(f"❌ Revenus ≤ 0: {len(revenus_problematiques):,} cas")
            incoherences.append('revenus_negatifs')
    
    # Ratio endettement > 100%
    if 'ratio_endettement' in df.columns:
        endettement_extreme = df[df['ratio_endettement'] > 5.0]  # Plus de 500%
        if len(endettement_extreme) > 0:
            print(f"❌ Ratio endettement > 500%: {len(endettement_extreme):,} cas")
            incoherences.append('endettement_extreme')
    
    # Reste à vivre négatif extrême
    if 'reste_a_vivre' in df.columns:
        reste_vivre_extreme = df[df['reste_a_vivre'] < -5000]
        if len(reste_vivre_extreme) > 0:
            print(f"❌ Reste à vivre < -5000 TND: {len(reste_vivre_extreme):,} cas")
            incoherences.append('reste_vivre_extreme')
    
    # 7. ANALYSE DISTRIBUTION DES VARIABLES CIBLES
    print(f"\n🔍 7. DISTRIBUTION VARIABLES CIBLES")
    print("-" * 60)
    
    # Decision finale
    if 'decision_finale' in df.columns:
        decisions = df['decision_finale'].value_counts()
        print(f"decision_finale:")
        for decision, count in decisions.items():
            pct = count / len(df) * 100
            print(f"  {decision}: {count:,} ({pct:.1f}%)")
        
        # Vérifier équilibre
        min_pct = min(decisions.values) / len(df) * 100
        if min_pct < 5:
            print(f"⚠️ Classe minoritaire < 5% - Risque de déséquilibre")
    
    # Classes de risque
    if 'classe_risque' in df.columns:
        classes = df['classe_risque'].value_counts().sort_index()
        print(f"\nclasse_risque:")
        for classe, count in classes.items():
            pct = count / len(df) * 100
            print(f"  {classe}: {count:,} ({pct:.1f}%)")
    
    # 8. ANALYSE VARIABLES CALCULÉES REDONDANTES
    print(f"\n🔍 8. VARIABLES CALCULÉES POTENTIELLEMENT REDONDANTES")
    print("-" * 60)
    
    # Vérifier si certaines variables sont des combinaisons d'autres
    variables_calculees = []
    
    # revenu_total = revenu_mensuel + autres_revenus ?
    if all(col in df.columns for col in ['revenu_total', 'revenu_mensuel', 'autres_revenus']):
        diff = abs(df['revenu_total'] - (df['revenu_mensuel'] + df['autres_revenus']))
        if (diff < 0.01).all():
            print(f"⚠️ revenu_total = revenu_mensuel + autres_revenus (redondant)")
            variables_calculees.append('revenu_total')
    
    # patrimoine_total = somme des actifs ?
    if all(col in df.columns for col in ['patrimoine_total', 'valeur_immobilier', 'valeur_vehicule', 'epargne']):
        patrimoine_calcule = df['valeur_immobilier'] + df['valeur_vehicule'] + df['epargne']
        diff = abs(df['patrimoine_total'] - patrimoine_calcule)
        if (diff < 0.01).mean() > 0.95:  # 95% des cas
            print(f"⚠️ patrimoine_total ≈ somme des actifs (potentiellement redondant)")
            variables_calculees.append('patrimoine_total')
    
    # 9. RECOMMANDATIONS DE NETTOYAGE
    print(f"\n📋 RECOMMANDATIONS DE NETTOYAGE")
    print("=" * 60)
    
    colonnes_a_supprimer = []
    
    # Colonnes constantes/quasi-constantes
    colonnes_a_supprimer.extend(colonnes_constantes)
    colonnes_a_supprimer.extend(colonnes_quasi_constantes)
    
    # Variables redondantes
    colonnes_a_supprimer.extend(variables_calculees)
    
    # Corrélations parfaites (garder une des deux)
    for col1, col2, corr in correlations_parfaites:
        if col2 not in colonnes_a_supprimer:
            colonnes_a_supprimer.append(col2)
    
    print(f"🗑️ COLONNES À SUPPRIMER ({len(colonnes_a_supprimer)}):")
    for col in colonnes_a_supprimer:
        print(f"   ❌ {col}")
    
    print(f"\n🔧 CORRECTIONS À APPLIQUER:")
    for incoh in incoherences:
        print(f"   ⚠️ Corriger {incoh}")
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Colonnes actuelles: {len(df.columns)}")
    print(f"   Colonnes à supprimer: {len(colonnes_a_supprimer)}")
    print(f"   Colonnes finales: {len(df.columns) - len(colonnes_a_supprimer)}")
    print(f"   Incohérences détectées: {len(incoherences)}")
    
    return {
        'colonnes_a_supprimer': colonnes_a_supprimer,
        'incoherences': incoherences,
        'correlations_parfaites': correlations_parfaites,
        'colonnes_aberrantes': colonnes_aberrantes
    }

if __name__ == "__main__":
    analyze_data_quality()
