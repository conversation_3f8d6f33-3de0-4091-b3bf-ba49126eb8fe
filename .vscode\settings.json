{"jest.jestCommandLine": "node_modules/.bin/jest", "eslint.workingDirectories": ["apps/backoffice-v2", "apps/workflows-dashboard", "packages/workflow-core", "services/workflows-service", "packages/common"], "search.exclude": {"**/node_modules": true, "**/dist": true, "**/data-migrations": false}, "search.followSymlinks": true, "search.useIgnoreFiles": false, "bookmarks.saveBookmarksInProject": true, "bookmarks.saveBookmarksInProjectFilePath": ".vscode/bookmarks.json"}