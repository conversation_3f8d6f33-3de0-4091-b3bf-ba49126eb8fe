import { ComponentProps, FunctionComponent, SVGProps } from 'react';
import { ctw } from '../../../utils/ctw/ctw';

/**
 * @description The Ballerine logo SVG found in Ballerine's Figma design.
 * @param props
 * @constructor
 */
export const BallerineLogo: FunctionComponent<SVGProps<SVGSVGElement>> = props => {
  return (
    <svg
      width="153"
      height="34"
      viewBox="0 0 153 34"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_2190_10239)">
        <path
          d="M15.0495 34C11.7759 34 9.24159 32.4362 7.99481 31.4763C7.16779 30.8415 7.02056 29.6617 7.66275 28.8443C8.30493 28.0268 9.49847 27.8812 10.3223 28.516C11.2151 29.2034 13.0571 30.343 15.3283 30.2439C19.1689 30.0767 22.7996 26.528 22.7996 22.9392C22.7996 21.1494 22.4644 19.9634 21.5622 18.5699L21.5278 18.5142C19.786 15.6004 16.0112 13.6402 12.1361 13.6402C11.1776 13.6402 10.2879 13.7641 9.48907 14.0118C8.48976 14.3215 7.4278 13.7703 7.11454 12.7825C6.80127 11.7947 7.35888 10.7419 8.35819 10.4353C9.52353 10.0761 10.7922 9.89343 12.133 9.89343C17.3708 9.89343 22.3266 12.5162 24.77 16.5789C26.0575 18.5823 26.5838 20.4279 26.5838 22.9392C26.5838 28.4727 21.2928 33.7399 15.4849 33.9907C15.3346 33.9969 15.1873 34 15.0401 34H15.0495Z"
          fill="currentColor"
        />
        <path
          d="M6.81973 26.2184C2.93214 26.2184 0 23.3727 0 19.6011C0 18.1736 0.523149 16.7244 1.55692 15.2938L1.59451 15.2442C1.71668 15.0708 1.85138 14.9005 1.99548 14.7302C2.09573 14.6095 2.42152 14.2224 2.94467 13.7207C3.6965 12.9993 4.8963 13.0178 5.6262 13.761C6.3561 14.5042 6.33731 15.6901 5.58548 16.4116C5.17197 16.808 4.94015 17.0929 4.93702 17.096L4.89316 17.1486C4.82111 17.2291 4.75846 17.3096 4.70521 17.3871L4.64255 17.4769C4.43267 17.7679 3.79048 18.6535 3.79048 19.6042C3.79048 21.2918 5.03413 22.4716 6.81973 22.4716C8.80582 22.4716 12.6339 21.9328 15.9451 18.3191L15.967 18.2974L16.5183 17.7153C17.2326 16.9597 18.4324 16.9195 19.1999 17.6286C19.9642 18.3346 20.005 19.5206 19.2876 20.2792L18.7488 20.849C14.4602 25.5217 9.43548 26.2215 6.82286 26.2215L6.81973 26.2184Z"
          fill="currentColor"
        />
        <path
          d="M9.12705 20.0439C8.19979 20.0439 7.38844 19.372 7.25373 18.4368C6.76818 15.1018 6.60841 12.0703 6.76504 9.16576C6.79324 4.10601 10.9659 0 16.0877 0C21.2096 0 25.4136 4.13388 25.4136 9.2184C25.4136 12.1849 24.5678 13.9375 23.2019 15.8047C22.5879 16.6439 21.4007 16.8297 20.5549 16.2228C19.7059 15.6158 19.518 14.4454 20.132 13.6062C21.1438 12.2251 21.6231 11.2157 21.6231 9.2184C21.6231 6.20237 19.1421 3.74991 16.0909 3.74991C13.0397 3.74991 10.5587 6.20237 10.5587 9.2184V9.32058C10.4083 11.9898 10.5555 14.7953 11.0066 17.9011C11.157 18.9261 10.4365 19.8767 9.39958 20.0253C9.30874 20.0377 9.21789 20.0439 9.12705 20.0439V20.0439Z"
          fill="currentColor"
        />
        <path
          d="M16.4715 17.7618L17.64 16.486C17.8154 16.3126 18.2602 16.2971 18.4701 16.486C18.7458 16.7337 19.2658 17.2942 19.6511 18.2913C19.8516 18.8146 15.964 19.5144 16.4715 17.7649V17.7618Z"
          fill="currentColor"
        />
        <path
          d="M2.87011 13.7703C3.50603 13.1789 4.2234 12.7578 4.87499 12.541C5.36994 12.3769 5.9996 12.3614 6.04346 13.0086C6.09045 13.7208 6.1625 14.5321 6.15937 15.0089C6.15937 15.5663 1.74863 15.2133 2.87011 13.7703Z"
          fill="currentColor"
        />
      </g>
      <path
        d="M41.0478 27.359H47.4084C52.0393 27.359 54.2711 25.0993 54.2711 21.8353C54.2711 19.0177 52.4857 17.3717 50.4492 16.8417C51.8441 16.2279 53.0158 14.8052 53.0158 12.7407C53.0158 10.1184 51.2024 7.8308 47.6316 7.8308H41.0478C40.7967 7.8308 40.6293 7.99819 40.6293 8.24926V26.9405C40.6293 27.1916 40.7967 27.359 41.0478 27.359ZM44.5628 23.8439V18.6829H47.1294C49.0543 18.6829 50.226 19.464 50.226 21.2495C50.226 23.0349 49.0543 23.8439 47.1294 23.8439H44.5628ZM44.5628 15.5863V11.3459H46.8504C48.329 11.3459 49.2496 11.8759 49.2496 13.3824C49.2496 14.9168 48.329 15.5863 46.8504 15.5863H44.5628ZM62.6626 27.6938C64.6991 27.6938 66.1777 26.829 67.1262 25.4341L67.182 26.9405C67.182 27.1916 67.3494 27.359 67.6005 27.359H70.3065C70.5576 27.359 70.7529 27.1916 70.7529 26.9405V14.1077C70.7529 13.8566 70.5855 13.6893 70.3344 13.6893H67.6005C67.3494 13.6893 67.182 13.8566 67.182 14.1077L67.1262 15.6142C66.2056 14.1914 64.727 13.3545 62.6626 13.3545C58.8128 13.3545 56.1625 16.479 56.1625 20.5241C56.1625 24.5972 58.8128 27.6938 62.6626 27.6938ZM59.9287 20.5241C59.9287 18.3481 61.4351 16.8696 63.4716 16.8696C65.5081 16.8696 66.9309 18.3202 66.9309 20.5241C66.9309 22.728 65.5081 24.1787 63.4716 24.1787C61.4351 24.1787 59.9287 22.728 59.9287 20.5241ZM74.4964 27.359H77.5372C77.7883 27.359 77.9556 27.1916 77.9556 26.9405V7.8308C77.9556 7.57972 77.7883 7.41234 77.5372 7.41234H74.4964C74.2453 7.41234 74.0779 7.57972 74.0779 7.8308V26.9405C74.0779 27.1916 74.2453 27.359 74.4964 27.359ZM81.6887 27.359H84.7295C84.9806 27.359 85.1479 27.1916 85.1479 26.9405V7.8308C85.1479 7.57972 84.9806 7.41234 84.7295 7.41234H81.6887C81.4376 7.41234 81.2702 7.57972 81.2702 7.8308V26.9405C81.2702 27.1916 81.4376 27.359 81.6887 27.359ZM95.2695 27.6938C97.1944 27.6938 98.9519 27.1358 100.124 25.9641C100.375 25.741 100.403 25.5457 100.263 25.3504L99.2309 23.9276C99.0914 23.7323 98.9519 23.7044 98.7567 23.816C97.6408 24.4577 96.6365 24.653 95.5764 24.653C93.3725 24.653 91.9497 23.7044 91.5312 21.9748H99.8447C101.044 21.9748 101.351 21.2216 101.351 19.8825C101.351 16.4232 98.9519 13.3545 94.7952 13.3545C90.5827 13.3545 87.7651 16.4511 87.7651 20.4683C87.7651 24.5972 90.778 27.6938 95.2695 27.6938ZM91.4754 19.2688C91.8102 17.4554 93.1214 16.5348 94.8231 16.5348C96.4691 16.5348 97.6966 17.4554 97.9197 19.2688H91.4754ZM104.355 27.359H107.368C107.619 27.359 107.787 27.1916 107.787 26.9405V20.1057C107.787 18.1529 108.791 16.8975 110.688 16.8975C111.079 16.8975 111.385 16.9533 111.692 17.037C112.055 17.1206 112.25 17.037 112.25 16.7022V14.2472C112.25 13.9961 112.195 13.8288 111.999 13.6893C111.776 13.5219 111.385 13.3545 110.744 13.3545C109.126 13.3545 108.261 14.4425 107.787 15.8932L107.675 14.1356C107.647 13.8009 107.508 13.6893 107.257 13.6893H104.355C104.104 13.6893 103.937 13.8566 103.937 14.1077V26.9405C103.937 27.1916 104.104 27.359 104.355 27.359ZM116.706 11.2622C117.934 11.2622 118.826 10.3137 118.826 9.11408C118.826 7.94239 117.934 6.99388 116.706 6.99388C115.479 6.99388 114.558 7.94239 114.558 9.11408C114.558 10.3137 115.479 11.2622 116.706 11.2622ZM114.753 26.9405C114.753 27.1916 114.921 27.359 115.172 27.359H118.213C118.464 27.359 118.631 27.1916 118.631 26.9405V14.1077C118.631 13.8566 118.464 13.6893 118.213 13.6893H115.172C114.921 13.6893 114.753 13.8566 114.753 14.1077V26.9405ZM122.391 27.359H125.404C125.655 27.359 125.822 27.1916 125.822 26.9405V19.8546C125.822 17.9297 126.882 16.8975 128.5 16.8975C130.146 16.8975 131.039 17.9297 131.039 19.8546V26.9405C131.039 27.1916 131.206 27.359 131.457 27.359H134.442C134.693 27.359 134.889 27.1916 134.889 26.9405V18.9898C134.889 15.4189 132.796 13.3545 129.951 13.3545C127.914 13.3545 126.603 14.3588 125.822 15.7537L125.71 14.1356C125.71 13.8009 125.543 13.6893 125.292 13.6893H122.391C122.14 13.6893 121.972 13.8566 121.972 14.1077V26.9405C121.972 27.1916 122.14 27.359 122.391 27.359ZM144.989 27.6938C146.914 27.6938 148.672 27.1358 149.843 25.9641C150.094 25.741 150.122 25.5457 149.983 25.3504L148.95 23.9276C148.811 23.7323 148.672 23.7044 148.476 23.816C147.36 24.4577 146.356 24.653 145.296 24.653C143.092 24.653 141.669 23.7044 141.251 21.9748H149.564C150.764 21.9748 151.071 21.2216 151.071 19.8825C151.071 16.4232 148.672 13.3545 144.515 13.3545C140.302 13.3545 137.485 16.4511 137.485 20.4683C137.485 24.5972 140.498 27.6938 144.989 27.6938ZM141.195 19.2688C141.53 17.4554 142.841 16.5348 144.543 16.5348C146.189 16.5348 147.416 17.4554 147.639 19.2688H141.195Z"
        fill="currentColor"
      />
      <defs>
        <clipPath id="clip0_2190_10239">
          <rect width="26.5897" height="34" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

/**
 * @description The home SVG found in Ballerine's Figma design.
 * @param props
 * @constructor
 */
export const HomeSvg: FunctionComponent<SVGProps<SVGSVGElement>> = props => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="h-6 w-6"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
      />
    </svg>
  );
};

/**
 * @description The checked checkbox SVG found in Ballerine's Figma design.
 * @param props
 * @constructor
 */
export const CheckedSvg: FunctionComponent<SVGProps<SVGSVGElement>> = props => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="h-6 w-6"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M9 12.75L11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 01-3.296-1.043 3.745 3.745 0 01-1.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.746 3.746 0 013.296-1.043A3.746 3.746 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 013.296 1.043 3.746 3.746 0 011.043 3.296A3.745 3.745 0 0121 12z"
      />
    </svg>
  );
};

/**
 * @description The cog (settings) SVG found in Ballerine's Figma design.
 * @param props
 * @constructor
 */
export const CogSvg: FunctionComponent<SVGProps<SVGSVGElement>> = props => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="h-6 w-6"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"
      />
      <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>
  );
};

/**
 * @description The log out SVG found in Ballerine's Figma design.
 * @param props
 * @constructor
 */
export const LogOutSvg: FunctionComponent<SVGProps<SVGSVGElement>> = props => {
  return (
    <svg
      width="15"
      height="14"
      viewBox="0 0 15 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        opacity="0.5"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.56185 2.5C8.56185 2.36193 8.44992 2.25 8.31185 2.25L1.97852 2.25C1.84044 2.25 1.72852 2.36193 1.72852 2.5L1.72852 11.5C1.72852 11.6381 1.84044 11.75 1.97851 11.75H8.31185C8.44992 11.75 8.56185 11.6381 8.56185 11.5V10.5C8.56185 10.0858 8.89763 9.75 9.31185 9.75C9.72606 9.75 10.0618 10.0858 10.0618 10.5V11.5C10.0618 12.4665 9.27835 13.25 8.31185 13.25H1.97851C1.01202 13.25 0.228516 12.4665 0.228516 11.5V2.5C0.228516 1.5335 1.01202 0.75 1.97852 0.75H8.31185C9.27835 0.75 10.0618 1.5335 10.0618 2.5V3.5C10.0618 3.91421 9.72606 4.25 9.31185 4.25C8.89763 4.25 8.56185 3.91421 8.56185 3.5V2.5ZM8.06879 7C8.06879 6.60113 8.39214 6.27778 8.79102 6.27778H12.2042L11.4159 5.521C11.1281 5.24477 11.1188 4.78758 11.395 4.49984C11.6712 4.2121 12.1284 4.20277 12.4162 4.479L14.4995 6.479C14.6414 6.61519 14.7216 6.80334 14.7216 7C14.7216 7.19666 14.6414 7.38481 14.4995 7.521L12.4162 9.521C12.1284 9.79723 11.6712 9.7879 11.395 9.50016C11.1188 9.21242 11.1281 8.75523 11.4159 8.479L12.2042 7.72222H8.79102C8.39214 7.72222 8.06879 7.39887 8.06879 7Z"
        fill="black"
      />
    </svg>
  );
};

/**
 * @description The magnifying glass SVG found in Ballerine's Figma design.
 * @param props
 * @constructor
 */
export const MagnifyingGlassSvg: FunctionComponent<SVGProps<SVGSVGElement>> = ({
  className,
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className={ctw('d-6', className)}
      {...props}
    >
      <path
        fillRule="evenodd"
        d="M10.5 3.75a6.75 6.75 0 100 13.5 6.75 6.75 0 000-13.5zM2.25 10.5a8.25 8.25 0 1114.59 5.28l4.69 4.69a.75.75 0 11-1.06 1.06l-4.69-4.69A8.25 8.25 0 012.25 10.5z"
        clipRule="evenodd"
      />
    </svg>
  );
};

/**
 * @description The filter SVG found in Ballerine's Figma design.
 * @param props
 * @constructor
 */
export const FilterSvg: FunctionComponent<SVGProps<SVGSVGElement>> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className={ctw('d-6', className)}
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 01-.659 1.591l-5.432 5.432a2.25 2.25 0 00-.659 1.591v2.927a2.25 2.25 0 01-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 00-.659-1.591L3.659 7.409A2.25 2.25 0 013 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0112 3z"
      />
    </svg>
  );
};

/**
 * @description The sort SVG found in Ballerine's Figma design.
 * @param props
 * @constructor
 */
export const SortSvg: FunctionComponent<SVGProps<SVGSVGElement>> = props => {
  return (
    <svg
      width="11"
      height="12"
      viewBox="0 0 11 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.6771 9.23291C10.9592 8.95087 10.9592 8.49358 10.6771 8.21153C10.3951 7.92949 9.93781 7.92949 9.65576 8.21153L8.88867 8.97862L8.88867 4.72222C8.88867 4.32335 8.56532 4 8.16645 4C7.76758 4 7.44423 4.32335 7.44423 4.72222L7.44423 8.97862L6.67714 8.21153C6.39509 7.92949 5.93781 7.92949 5.65576 8.21153C5.37372 8.49358 5.37372 8.95087 5.65576 9.23291L7.65576 11.2329C7.93781 11.515 8.39509 11.515 8.67714 11.2329L10.6771 9.23291Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.211534 2.2124C-0.0705113 2.49445 -0.0705113 2.95173 0.211534 3.23378C0.493579 3.51582 0.950865 3.51582 1.23291 3.23378L2 2.46669L2 6.72309C2 7.12196 2.32335 7.44531 2.72222 7.44531C3.12109 7.44531 3.44444 7.12196 3.44444 6.72309L3.44444 2.46669L4.21153 3.23378C4.49358 3.51582 4.95087 3.51582 5.23291 3.23378C5.51496 2.95173 5.51496 2.49445 5.23291 2.2124L3.23291 0.212402C2.95087 -0.069643 2.49358 -0.069643 2.21153 0.212402L0.211534 2.2124Z"
        fill="currentColor"
      />
    </svg>
  );
};

/**
 * @description The rejected SVG found in Ballerine's Figma design.
 * @param props
 * @constructor
 */
export const RejectedSvg: FunctionComponent<SVGProps<SVGSVGElement>> = props => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="h-6 w-6"
      {...props}
    >
      <path
        fillRule="evenodd"
        d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zm-1.72 6.97a.75.75 0 10-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 101.06 1.06L12 13.06l1.72 1.72a.75.75 0 101.06-1.06L13.06 12l1.72-1.72a.75.75 0 10-1.06-1.06L12 10.94l-1.72-1.72z"
        clipRule="evenodd"
      />
    </svg>
  );
};

/**
 * @description The approved SVG found in Ballerine's Figma design.
 * @param props
 * @constructor
 */
export const ApprovedSvg: FunctionComponent<SVGProps<SVGSVGElement>> = props => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="h-6 w-6"
      {...props}
    >
      <path
        fillRule="evenodd"
        d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
        clipRule="evenodd"
      />
    </svg>
  );
};

/**
 * @description The chevron SVG found in Ballerine's Figma design.
 * @param props
 * @constructor
 */
export const ChevronDownSvg: FunctionComponent<SVGProps<SVGSVGElement>> = ({
  className,
  ...rest
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className={ctw('h-6 w-6', className)}
      {...rest}
    >
      <path
        fillRule="evenodd"
        d="M12.53 16.28a.75.75 0 01-1.06 0l-7.5-7.5a.75.75 0 011.06-1.06L12 14.69l6.97-6.97a.75.75 0 111.06 1.06l-7.5 7.5z"
        clipRule="evenodd"
      />
    </svg>
  );
};

/**
 * @description The warning SVG found in Ballerine's Figma design.
 * @param props
 * @constructor
 */
export const WarningSvg: FunctionComponent<SVGProps<SVGSVGElement>> = props => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="h-6 w-6"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
      />
    </svg>
  );
};

/**
 * @description An SVG of an 'X' from Heroicons.
 *
 * @see {@link https://heroicons.com/|Heroicons}
 */
export const XMarkSvg: FunctionComponent<SVGProps<SVGSVGElement>> = props => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="h-6 w-6"
      {...props}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
    </svg>
  );
};

/**
 * @description The ellipsis SVG found in Ballerine's Figma design.
 * @param props
 * @constructor
 */
export const EllipsisSvg: FunctionComponent<SVGProps<SVGSVGElement>> = props => {
  return (
    <svg
      width="3"
      height="17"
      viewBox="0 0 3 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle cx="1.5" cy="1.5" r="1.5" fill="currentColor" />
      <circle cx="1.5" cy="8.5" r="1.5" fill="currentColor" />
      <circle cx="1.5" cy="15.5" r="1.5" fill="currentColor" />
    </svg>
  );
};

/**
 * @description A chevron left SVG from Heroicons.
 *
 * @see {@link https://heroicons.com/|Heroicons}
 */
export const ChevronLeftSvg: FunctionComponent<SVGProps<SVGSVGElement>> = props => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="h-6 w-6"
      {...props}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
    </svg>
  );
};

/**
 * @description A chevron right SVG from Heroicons.
 *
 * @see {@link https://heroicons.com/|Heroicons}
 */
export const ChevronRightSvg: FunctionComponent<SVGProps<SVGSVGElement>> = props => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="h-6 w-6"
      {...props}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
    </svg>
  );
};

/**
 * @description An SVG of a checkmark from Heroicons.
 *
 * @see {@link https://heroicons.com/|Heroicons}
 */
export const CheckSvg: FunctionComponent<SVGProps<SVGSVGElement>> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className={ctw(`h-6 w-6`, className)}
      {...props}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12.75l6 6 9-13.5" />
    </svg>
  );
};

export const PhotoSvg = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="h-6 w-6"
      {...props}
    >
      <path
        fillRule="evenodd"
        d="M1.5 6a2.25 2.25 0 012.25-2.25h16.5A2.25 2.25 0 0122.5 6v12a2.25 2.25 0 01-2.25 2.25H3.75A2.25 2.25 0 011.5 18V6zM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0021 18v-1.94l-2.69-2.689a1.5 1.5 0 00-2.12 0l-.88.879.97.97a.75.75 0 11-1.06 1.06l-5.16-5.159a1.5 1.5 0 00-2.12 0L3 16.061zm10.125-7.81a1.125 1.125 0 112.25 0 1.125 1.125 0 01-2.25 0z"
        clipRule="evenodd"
      />
    </svg>
  );
};

export const NoCasesSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 96 81" fill="none" {...props}>
    <circle cx="45.8955" cy="42" r="39" fill="#D9D9D9" />
    <path
      d="M64.9549 27.3496H25.8361C23.9378 27.3496 22.1013 28.0247 20.6549 29.2541L2.89551 44.3496V67.5H87.8955V44.3496L70.1361 29.2541C68.6897 28.0247 66.8532 27.3496 64.9549 27.3496Z"
      fill="#E7E7E7"
    />
    <path
      d="M87.8955 44.3496L70.1361 29.2541C68.6897 28.0247 66.8532 27.3496 64.9549 27.3496H25.8361C23.9378 27.3496 22.1013 28.0247 20.6549 29.2541L2.89551 44.3496"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M1.89551 47C1.89551 44.7909 3.68637 43 5.89551 43H18.1336C21.028 43 23.7804 44.2541 25.6796 46.4382L29.1022 50.3741C31.3813 52.9951 34.6842 54.5 38.1574 54.5H52.6336C56.1068 54.5 59.4097 52.9951 61.6888 50.3741L65.1114 46.4382C67.0106 44.2541 69.763 43 72.6574 43H84.8955C87.1046 43 88.8955 44.7909 88.8955 47V64C88.8955 66.2091 87.1046 68 84.8955 68H5.89551C3.68637 68 1.89551 66.2091 1.89551 64V47Z"
      fill="white"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M73.8955 38C81.1492 36.6795 90.4869 28.6125 85.2113 20.2091C81.1492 13.7387 71.7109 17.4116 75.9266 24.0326C79.1106 29.0334 88.9059 25.3056 90.6338 17.1607M89.2736 8C89.5981 8.67957 89.8695 9.34662 90.0916 10M90.7924 13C90.8134 13.1679 90.8311 13.3345 90.8455 13.5"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
    />
    <circle
      cx="86.2744"
      cy="3.41506"
      r="2.5"
      transform="rotate(-30 86.2744 3.41506)"
      fill="black"
    />
    <ellipse cx="91.6045" cy="2.64648" rx="3.5" ry="1.5" fill="black" />
    <ellipse
      cx="82.9445"
      cy="7.64665"
      rx="3.5"
      ry="1.5"
      transform="rotate(-60 82.9445 7.64665)"
      fill="black"
    />
  </svg>
);

export const NoTasksSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="80"
    height="92"
    viewBox="0 0 80 92"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx="39" cy="53" r="39" fill="#D9D9D9" />
    <path
      d="M6 37C6 35.3431 7.34315 34 9 34H68C69.6569 34 71 35.3431 71 37V82C71 83.6569 69.6569 85 68 85H9C7.34315 85 6 83.6569 6 82V37Z"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M46 1L48.5 1.5L57 9L65 16.5L66 19H50C47.7909 19 46 17.2091 46 15V1Z"
      fill="#B8B8B8"
      fillOpacity="0.6"
    />
    <path
      d="M11 5C11 2.79086 12.7909 1 15 1H46.4717C47.4559 1 48.4055 1.36281 49.1389 2.01903L57.5 9.5L64.7415 16.3155C65.5446 17.0714 66 18.1254 66 19.2283V61.5C66 63.7091 64.2091 65.5 62 65.5H15C12.7909 65.5 11 63.7091 11 61.5V5Z"
      fill="white"
      fillOpacity="0.6"
      stroke="#9B9B9B"
      strokeWidth="2"
      strokeLinecap="round"
      strokeDasharray="4 8"
    />
    <path
      d="M46 4.5V15C46 17.2091 47.7909 19 50 19H62.5"
      stroke="#9B9B9B"
      strokeWidth="2"
      strokeLinecap="round"
      strokeDasharray="4 8"
    />
    <path
      d="M14.9839 41.4088C15.2655 40.0079 16.4961 39 17.925 39H46.6364C47.2914 39 47.9284 39.2143 48.4501 39.6103L52.4461 42.643C53.3156 43.303 54.3772 43.6602 55.4688 43.6602H62.4561H75.1576C77.0417 43.6602 78.4594 45.3765 78.1036 47.2267L71.3085 82.5665C71.0369 83.979 69.8009 85 68.3625 85H9.88411C7.98977 85 6.56961 83.2659 6.94295 81.4088L14.9839 41.4088Z"
      fill="white"
      stroke="black"
      strokeWidth="2"
    />
  </svg>
);

export const DoubleCaretSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="6"
    height="10"
    viewBox="0 0 6 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M5 7.00293L3 9.00293L1 7.00293"
      stroke="#A3A3A3"
      strokeWidth="1.44444"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1 3L3 1L5 3"
      stroke="#A3A3A3"
      strokeWidth="1.44444"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const UnassignedAvatarSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx="12" cy="12" r="10" stroke="#E4E4E7" strokeWidth="2" />
    <path
      d="M16.8285 16.9763C17.5786 17.6014 18 18.4493 18 19.3333C13.6001 22.8 8.1667 20.7778 6 19.3333C6 18.4493 6.42143 17.6014 7.17158 16.9763C7.92173 16.3512 8.93915 16 10 16H14C15.0609 16 16.0783 16.3512 16.8285 16.9763Z"
      fill="#E4E4E7"
      stroke="#E4E4E7"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 13C14.2091 13 16 11.2091 16 9C16 6.79086 14.2091 5 12 5C9.79086 5 8 6.79086 8 9C8 11.2091 9.79086 13 12 13Z"
      fill="#E4E4E7"
    />
  </svg>
);

export const CopySvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_12422_26452)">
      <path
        d="M2.66659 10.6668C1.93325 10.6668 1.33325 10.0668 1.33325 9.3335V2.66683C1.33325 1.9335 1.93325 1.3335 2.66659 1.3335H9.33325C10.0666 1.3335 10.6666 1.9335 10.6666 2.66683M6.66658 5.3335H13.3333C14.0696 5.3335 14.6666 5.93045 14.6666 6.66683V13.3335C14.6666 14.0699 14.0696 14.6668 13.3333 14.6668H6.66658C5.93021 14.6668 5.33325 14.0699 5.33325 13.3335V6.66683C5.33325 5.93045 5.93021 5.3335 6.66658 5.3335Z"
        stroke="#787981"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_12422_26452">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const DownloadFileSvg: FunctionComponent<ComponentProps<'svg'>> = props => (
  <svg
    width="80"
    height="92"
    viewBox="0 0 80 92"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={`mb-2.5`}
    {...props}
  >
    <circle cx="39" cy="53" r="39" fill="#D9D9D9" />
    <path
      d="M6 37C6 35.3431 7.34315 34 9 34H68C69.6569 34 71 35.3431 71 37V82C71 83.6569 69.6569 85 68 85H9C7.34315 85 6 83.6569 6 82V37Z"
      fill="#D9D9D9"
      stroke="#9B9B9B"
      strokeWidth="2"
    />
    <path
      d="M46 1L48.5 1.5L57 9L65 16.5L66 19H50C47.7909 19 46 17.2091 46 15V1Z"
      fill="#B8B8B8"
      fillOpacity="0.6"
    />
    <path
      d="M11 5C11 2.79086 12.7909 1 15 1H46.4717C47.4559 1 48.4055 1.36281 49.1389 2.01903L57.5 9.5L64.7415 16.3155C65.5446 17.0714 66 18.1254 66 19.2283V61.5C66 63.7091 64.2091 65.5 62 65.5H15C12.7909 65.5 11 63.7091 11 61.5V5Z"
      fill="white"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
    />
    <path
      d="M14.961 43.3841C15.2524 41.9949 16.4776 41 17.8971 41H46.6816C47.3157 41 47.9335 41.2009 48.4462 41.5739L52.4826 44.5096L53.0708 43.7009L52.4826 44.5096C53.3373 45.1312 54.3668 45.466 55.4236 45.466H62.4561H75.1177C77.0116 45.466 78.4317 47.1993 78.0591 49.0562L71.3309 82.5902C71.0497 83.9916 69.8189 85 68.3895 85H9.92611C8.02149 85 6.599 83.2482 6.99001 81.3841L14.961 43.3841Z"
      fill="white"
      stroke="#9B9B9B"
      strokeWidth="2"
    />
    <path
      d="M60 18.5V34C60 35.1046 59.1046 36 58 36H19C17.8954 36 17 35.1046 17 34V17C17 15.8954 17.8954 15 19 15L46 15"
      stroke="#9B9B9B"
      strokeWidth="2"
    />
    <path d="M18 22H59" stroke="#9B9B9B" strokeWidth="2" />
    <path d="M18 29H59" stroke="#9B9B9B" strokeWidth="2" />
    <path d="M28 16L28 35" stroke="#9B9B9B" strokeWidth="2" />
    <path d="M39 16L39 35" stroke="#9B9B9B" strokeWidth="2" />
    <path d="M49 18L49 35" stroke="#9B9B9B" strokeWidth="2" />
    <path
      d="M46 5V14C46 16.2091 47.7909 18 50 18H62"
      stroke="black"
      strokeWidth="2"
      strokeLinecap="round"
    />
  </svg>
);

export const NoIndividualsSvg: FunctionComponent<ComponentProps<'svg'>> = props => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="80" height="79" viewBox="0 0 80 79" fill="none">
      <circle cx="39" cy="40" r="39" fill="#D9D9D9" />
      <path
        d="M9 24H68C69.6569 24 71 25.3431 71 27V72C71 73.6569 69.6569 75 68 75H9C7.34315 75 6 73.6569 6 72V27C6 25.3431 7.34315 24 9 24Z"
        fill="#D9D9D9"
        stroke="black"
        strokeWidth="2"
      />
      <path
        d="M68 39V33.6667C68 31.8986 67.2475 30.2029 65.9079 28.9526C64.5684 27.7024 62.7515 27 60.8571 27H50.1429C48.2485 27 46.4316 27.7024 45.0921 28.9526C43.7525 30.2029 43 31.8986 43 33.6667V39"
        fill="white"
        fillOpacity="0.3"
      />
      <path
        d="M68 39V33.6667C68 31.8986 67.2475 30.2029 65.9079 28.9526C64.5684 27.7024 62.7515 27 60.8571 27H50.1429C48.2485 27 46.4316 27.7024 45.0921 28.9526C43.7525 30.2029 43 31.8986 43 33.6667V39"
        stroke="#9B9B9B"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeDasharray="4 4"
      />
      <path
        d="M48 30V26.6667C48 24.8986 47.2475 23.2029 45.9079 21.9526C44.5684 20.7024 42.7515 20 40.8571 20H30.1429C28.2485 20 26.4316 20.7024 25.0921 21.9526C23.7525 23.2029 23 24.8986 23 26.6667V30"
        fill="white"
        fillOpacity="0.6"
      />
      <path
        d="M48 30V26.6667C48 24.8986 47.2475 23.2029 45.9079 21.9526C44.5684 20.7024 42.7515 20 40.8571 20H30.1429C28.2485 20 26.4316 20.7024 25.0921 21.9526C23.7525 23.2029 23 24.8986 23 26.6667V30"
        stroke="#9B9B9B"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeDasharray="4 4"
      />
      <path
        d="M55.5 23C59.6421 23 63 19.6421 63 15.5C63 11.3579 59.6421 8 55.5 8C51.3579 8 48 11.3579 48 15.5C48 19.6421 51.3579 23 55.5 23Z"
        fill="white"
        fillOpacity="0.3"
        stroke="#9B9B9B"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeDasharray="3 4"
      />
      <path
        d="M35.5 16C39.6421 16 43 12.6421 43 8.5C43 4.35786 39.6421 1 35.5 1C31.3579 1 28 4.35786 28 8.5C28 12.6421 31.3579 16 35.5 16Z"
        fill="white"
        fillOpacity="0.6"
        stroke="#9B9B9B"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeDasharray="3 4"
      />
      <path
        d="M17.9248 29H46.6367C47.2097 29.0001 47.7687 29.1645 48.249 29.4707L48.4502 29.6104L52.4463 32.6426C53.3158 33.3025 54.3772 33.6601 55.4688 33.6602H75.1572C76.9824 33.6602 78.3704 35.2709 78.1318 37.0537L78.1035 37.2266L71.3086 72.5664C71.037 73.979 69.8007 75 68.3623 75H9.88379C8.04866 74.9998 6.6585 73.3723 6.91309 71.582L6.94336 71.4092L14.9834 31.4092C15.265 30.0083 16.496 29.0001 17.9248 29Z"
        fill="white"
        stroke="black"
        strokeWidth="2"
      />
    </svg>
  );
};
