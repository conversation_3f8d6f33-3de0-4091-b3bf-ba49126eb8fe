---
description: 
globs: 
alwaysApply: true
---
Write comments thoughtfully:
- Do NOT write comments that explain obvious code or restate WHAT the code does.
- Comments should primarily explain WHY code exists or WHY a particular approach was chosen.
- Only add comments for complex, non-intuitive logic where the code itself doesn't clearly communicate intent.
- Avoid unnecessary comments that add visual noise without adding value.
- Write comments only when they provide genuine insight or when explicitly requested.
