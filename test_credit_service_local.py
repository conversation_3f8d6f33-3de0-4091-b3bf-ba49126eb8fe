#!/usr/bin/env python3
"""
Test local du service de scoring de crédit avec optimisations Intel
Utilise les données synthétiques générées pour tester les performances
"""

import sys
import os
import time
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, roc_auc_score
import warnings
warnings.filterwarnings('ignore')

# Configuration optimisations CPU
os.environ['OMP_NUM_THREADS'] = '4'
os.environ['MKL_NUM_THREADS'] = '4'

def load_optimized_data():
    """Charge les données optimisées"""
    try:
        data_path = "data_generation/output/tunisian_credit_multicategories_100k_optimized.csv"
        print(f"🔄 Chargement des données depuis {data_path}")

        df = pd.read_csv(data_path)
        print(f"✅ Données chargées: {df.shape[0]:,} clients, {df.shape[1]} colonnes")

        # Affichage des informations sur les catégories
        if 'categorie_client' in df.columns:
            print("\n📊 Distribution des catégories:")
            print(df['categorie_client'].value_counts())

        return df
    except FileNotFoundError:
        print(f"❌ Fichier non trouvé: {data_path}")
        return None
    except Exception as e:
        print(f"❌ Erreur lors du chargement: {e}")
        return None

def prepare_features(df):
    """Prépare les features pour le modèle"""
    print("\n🔧 Préparation des features...")

    # Colonnes à exclure du modèle
    exclude_cols = [
        'client_id', 'nom', 'prenom', 'date_naissance', 'adresse',
        'telephone', 'email', 'date_creation_dossier', 'numero_compte',
        'iban', 'date_ouverture_compte', 'agence_bancaire'
    ]

    # Sélection des features numériques
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    feature_cols = [col for col in numeric_cols if col not in exclude_cols]

    # Vérification de la présence de la variable cible
    if 'classe_risque' not in df.columns:
        print("❌ Variable cible 'classe_risque' non trouvée")
        return None, None, None

    X = df[feature_cols].copy()
    y = df['classe_risque'].copy()

    print(f"✅ Features sélectionnées: {len(feature_cols)} colonnes")
    print(f"✅ Variable cible: classe_risque (classes: {sorted(y.unique())})")

    return X, y, feature_cols

def train_model(X, y):
    """Entraîne le modèle de scoring"""
    print("\n🤖 Entraînement du modèle...")

    # Division train/test
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )

    print(f"📊 Données d'entraînement: {X_train.shape[0]:,} échantillons")
    print(f"📊 Données de test: {X_test.shape[0]:,} échantillons")

    # Normalisation
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # Modèle Random Forest optimisé pour CPU
    start_time = time.time()

    model = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        n_jobs=4,  # Utilise 4 cores
        random_state=42,
        verbose=1
    )

    print("🔄 Entraînement en cours...")
    model.fit(X_train_scaled, y_train)

    training_time = time.time() - start_time
    print(f"✅ Entraînement terminé en {training_time:.2f} secondes")

    # Évaluation
    print("\n📈 Évaluation du modèle...")
    y_pred = model.predict(X_test_scaled)
    y_pred_proba = model.predict_proba(X_test_scaled)

    # Métriques
    print("\n📊 Rapport de classification:")
    print(classification_report(y_test, y_pred))

    # AUC pour classification multiclasse
    try:
        auc_score = roc_auc_score(y_test, y_pred_proba, multi_class='ovr')
        print(f"🎯 AUC Score: {auc_score:.4f}")
    except Exception as e:
        print(f"⚠️ Impossible de calculer l'AUC: {e}")

    return model, scaler, X_test_scaled, y_test

def test_prediction_speed(model, scaler, X_sample):
    """Test la vitesse de prédiction"""
    print("\n⚡ Test de vitesse de prédiction...")

    # Test avec un échantillon
    n_predictions = 1000
    sample_data = X_sample[:n_predictions]

    start_time = time.time()
    predictions = model.predict(sample_data)
    prediction_time = time.time() - start_time

    avg_time_ms = (prediction_time / n_predictions) * 1000

    print(f"✅ {n_predictions} prédictions en {prediction_time:.4f} secondes")
    print(f"⚡ Temps moyen par prédiction: {avg_time_ms:.2f} ms")
    print(f"🚀 Débit: {n_predictions/prediction_time:.0f} prédictions/seconde")

    return predictions

def display_feature_importance(model, feature_cols):
    """Affiche l'importance des features"""
    print("\n🔍 Importance des features (Top 10):")

    importances = model.feature_importances_
    feature_importance = pd.DataFrame({
        'feature': feature_cols,
        'importance': importances
    }).sort_values('importance', ascending=False)

    top_features = feature_importance.head(10)
    for idx, row in top_features.iterrows():
        print(f"  {row['feature']}: {row['importance']:.4f}")

    return feature_importance

def main():
    """Fonction principale"""
    print("🚀 DÉMARRAGE DU TEST LOCAL DU SERVICE DE SCORING")
    print("=" * 60)

    # Chargement des données
    df = load_optimized_data()
    if df is None:
        return

    # Préparation des features
    X, y, feature_cols = prepare_features(df)
    if X is None:
        return

    # Entraînement du modèle
    model, scaler, X_test, y_test = train_model(X, y)

    # Test de vitesse
    test_prediction_speed(model, scaler, X_test)

    # Importance des features
    display_feature_importance(model, feature_cols)

    print("\n" + "=" * 60)
    print("✅ TEST TERMINÉ AVEC SUCCÈS!")
    print("🎯 Le modèle est prêt pour l'intégration dans l'API")

    # Informations système
    print(f"\n💻 Configuration système:")
    print(f"   - Threads OMP: {os.environ.get('OMP_NUM_THREADS', 'non défini')}")
    print(f"   - Threads MKL: {os.environ.get('MKL_NUM_THREADS', 'non défini')}")

if __name__ == "__main__":
    main()
