#!/usr/bin/env python3
"""
Démarrage local de Ballerine avec service de scoring intégré
Alternative à Docker pour tester rapidement le système
"""

import os
import sys
import time
import threading
import subprocess
from flask import Flask, request, jsonify
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import joblib
import warnings
warnings.filterwarnings('ignore')

# Configuration
POSTGRES_PORT = 5432
WORKFLOWS_PORT = 3001
SCORING_PORT = 5001

class CreditScoringService:
    """Service de scoring de crédit optimisé"""
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.feature_cols = None
        self.is_trained = False
        
    def load_and_train(self):
        """Charge les données et entraîne le modèle"""
        try:
            print("🔄 Chargement et entraînement du modèle de scoring...")
            
            # Chargement des données
            data_path = "data_generation/output/tunisian_credit_multicategories_100k_optimized.csv"
            df = pd.read_csv(data_path)
            
            # Préparation des features
            exclude_cols = [
                'client_id', 'cin', 'nationalite', 'pays_residence', 
                'situation_familiale', 'region', 'profession', 'secteur_activite',
                'type_contrat', 'type_logement', 'banque_principale', 'type_credit',
                'classe_risque', 'decision_finale'
            ]
            
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            self.feature_cols = [col for col in numeric_cols if col not in exclude_cols]
            
            X = df[self.feature_cols].copy()
            y = df['classe_risque'].copy()
            
            # Entraînement rapide
            self.scaler = StandardScaler()
            X_scaled = self.scaler.fit_transform(X)
            
            self.model = RandomForestClassifier(
                n_estimators=50,  # Réduit pour vitesse
                max_depth=8,
                n_jobs=4,
                random_state=42
            )
            
            self.model.fit(X_scaled, y)
            self.is_trained = True
            
            print(f"✅ Modèle entraîné avec {len(self.feature_cols)} features")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de l'entraînement: {e}")
            return False
    
    def predict(self, client_data):
        """Effectue une prédiction de scoring"""
        if not self.is_trained:
            return {"error": "Modèle non entraîné"}
        
        try:
            # Préparation des données client
            features = []
            for col in self.feature_cols:
                features.append(client_data.get(col, 0))
            
            # Prédiction
            X_client = np.array(features).reshape(1, -1)
            X_client_scaled = self.scaler.transform(X_client)
            
            prediction = self.model.predict(X_client_scaled)[0]
            probabilities = self.model.predict_proba(X_client_scaled)[0]
            
            # Calcul du score de confiance
            confidence = max(probabilities)
            
            return {
                "classe_risque": prediction,
                "confidence": float(confidence),
                "probabilities": {
                    f"C{i}": float(prob) for i, prob in enumerate(probabilities)
                },
                "status": "success"
            }
            
        except Exception as e:
            return {"error": f"Erreur de prédiction: {str(e)}"}

# Instance globale du service
scoring_service = CreditScoringService()

def create_scoring_app():
    """Crée l'application Flask de scoring"""
    app = Flask(__name__)
    
    @app.route('/health', methods=['GET'])
    def health():
        return jsonify({
            "status": "healthy",
            "service": "credit-scoring",
            "model_trained": scoring_service.is_trained,
            "timestamp": time.time()
        })
    
    @app.route('/score', methods=['POST'])
    def score_credit():
        try:
            client_data = request.get_json()
            if not client_data:
                return jsonify({"error": "Données client manquantes"}), 400
            
            result = scoring_service.predict(client_data)
            return jsonify(result)
            
        except Exception as e:
            return jsonify({"error": str(e)}), 500
    
    @app.route('/model/info', methods=['GET'])
    def model_info():
        if not scoring_service.is_trained:
            return jsonify({"error": "Modèle non entraîné"}), 400
        
        return jsonify({
            "features_count": len(scoring_service.feature_cols),
            "features": scoring_service.feature_cols[:10],  # Top 10
            "model_type": "RandomForestClassifier",
            "classes": ["C0", "C1", "C2", "C3", "C4", "C5"]
        })
    
    return app

def check_postgres():
    """Vérifie si PostgreSQL est disponible"""
    try:
        import psycopg2
        conn = psycopg2.connect(
            host="localhost",
            port=POSTGRES_PORT,
            database="ballerine",
            user="ballerine",
            password="password"
        )
        conn.close()
        return True
    except:
        return False

def start_postgres_local():
    """Démarre PostgreSQL localement si possible"""
    print("🔄 Vérification de PostgreSQL...")
    
    if check_postgres():
        print("✅ PostgreSQL déjà disponible")
        return True
    
    print("⚠️ PostgreSQL non disponible - utilisation d'une base de données simulée")
    return False

def start_scoring_service():
    """Démarre le service de scoring"""
    print(f"🚀 Démarrage du service de scoring sur le port {SCORING_PORT}...")
    
    # Entraînement du modèle
    if not scoring_service.load_and_train():
        print("❌ Impossible d'entraîner le modèle")
        return False
    
    # Démarrage de l'API
    app = create_scoring_app()
    
    def run_app():
        app.run(host='0.0.0.0', port=SCORING_PORT, debug=False)
    
    thread = threading.Thread(target=run_app, daemon=True)
    thread.start()
    
    # Test de connectivité
    time.sleep(2)
    try:
        import requests
        response = requests.get(f"http://localhost:{SCORING_PORT}/health")
        if response.status_code == 200:
            print(f"✅ Service de scoring démarré sur http://localhost:{SCORING_PORT}")
            return True
    except:
        pass
    
    print("❌ Impossible de démarrer le service de scoring")
    return False

def test_scoring_api():
    """Test rapide de l'API de scoring"""
    print("\n🧪 Test de l'API de scoring...")
    
    try:
        import requests
        
        # Test des données d'exemple
        test_data = {
            "age": 35,
            "revenu_mensuel": 1500,
            "ratio_endettement": 25.5,
            "score_comportement": 0.85,
            "anciennete_emploi": 5,
            "nombre_incidents_12m": 0
        }
        
        response = requests.post(
            f"http://localhost:{SCORING_PORT}/score",
            json=test_data
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Test réussi - Classe de risque: {result.get('classe_risque')}")
            print(f"   Confiance: {result.get('confidence', 0):.3f}")
            return True
        else:
            print(f"❌ Test échoué - Code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 DÉMARRAGE DE BALLERINE EN MODE LOCAL")
    print("=" * 60)
    
    # Vérification des prérequis
    print("🔍 Vérification des prérequis...")
    
    # PostgreSQL
    postgres_ok = start_postgres_local()
    
    # Service de scoring
    scoring_ok = start_scoring_service()
    
    if not scoring_ok:
        print("❌ Impossible de démarrer les services essentiels")
        return
    
    # Test de l'API
    test_scoring_api()
    
    print("\n" + "=" * 60)
    print("✅ BALLERINE DÉMARRÉ AVEC SUCCÈS!")
    print(f"🌐 Service de scoring: http://localhost:{SCORING_PORT}")
    print(f"📊 Health check: http://localhost:{SCORING_PORT}/health")
    print(f"🎯 API de scoring: http://localhost:{SCORING_PORT}/score")
    print(f"ℹ️ Informations modèle: http://localhost:{SCORING_PORT}/model/info")
    
    if postgres_ok:
        print(f"🗄️ PostgreSQL: localhost:{POSTGRES_PORT}")
    
    print("\n⌨️ Appuyez sur Ctrl+C pour arrêter...")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 Arrêt de Ballerine...")

if __name__ == "__main__":
    main()
