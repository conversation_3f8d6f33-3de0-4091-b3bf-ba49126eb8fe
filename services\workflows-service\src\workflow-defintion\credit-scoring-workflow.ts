import { StateTag } from '@ballerine/common';

export const creditScoringWorkflowDefinition = {
  id: 'credit_scoring_workflow',
  name: 'Credit Scoring Workflow',
  version: 1,
  definitionType: 'statechart-json',
  definition: {
    id: 'credit_scoring_workflow_v1',
    predictableActionArguments: true,
    initial: 'idle',
    states: {
      idle: {
        on: {
          START: 'collect_documents',
        },
      },
      collect_documents: {
        tags: [StateTag.COLLECTION_FLOW],
        on: {
          DOCUMENTS_COLLECTED: 'kyc_verification',
        },
      },
      kyc_verification: {
        on: {
          KYC_APPROVED: 'run_credit_scoring',
          KYC_REJECTED: 'rejected',
        },
      },
      run_credit_scoring: {
        on: {
          SCORING_COMPLETED: 'manual_review',
          SCORING_FAILED: 'scoring_error',
        },
      },
      scoring_error: {
        on: {
          RETRY_SCORING: 'run_credit_scoring',
          MANUAL_REVIEW: 'manual_review',
        },
      },
      manual_review: {
        tags: [StateTag.MANUAL_REVIEW],
        on: {
          APPROVE: 'approved',
          REJECT: 'rejected',
          REQUEST_MORE_INFO: 'collect_documents',
        },
      },
      approved: {
        tags: [StateTag.APPROVED],
        type: 'final',
      },
      rejected: {
        tags: [StateTag.REJECTED],
        type: 'final',
      },
    },
  },
  extensions: {
    apiPlugins: [
      {
        name: 'creditScoringPlugin',
        pluginKind: 'api',
        url: 'http://credit-scoring-service:5000/api/score',
        method: 'POST',
        stateNames: ['run_credit_scoring'],
        successAction: 'SCORING_COMPLETED',
        errorAction: 'SCORING_FAILED',
        request: {
          transform: [
            {
              transformer: 'jmespath',
              mapping: `{
                customerId: entity.ballerineEntityId,
                personalInfo: entity.data.personalInfo,
                financialInfo: entity.data.financialInfo,
                documents: documents
              }`,
            },
          ],
        },
      }
    ],
    commonPlugins: [
      {
        pluginKind: 'riskRules',
        name: 'creditRiskEvaluation',
        stateNames: ['manual_review'],
        rulesSource: {
          source: 'notion',
          databaseId: 'your_notion_database_id',
        },
      },
    ],
  },
};