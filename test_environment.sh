#!/bin/bash

# Démarrage et test de l'environnement complet

# 1. Construire et démarrer les services avec Docker Compose
echo "Démarrage des services..."
docker-compose up -d

# 2. Attendre que les services soient prêts
echo "Attente du démarrage des services..."
sleep 10

# 3. Vérifier l'état des services
echo "Vérification de l'état des services..."
docker-compose ps

# 4. Tester le service de workflows
echo "Test du service de workflows..."
curl -s http://localhost:3000/api/v1/_health/ready

# 5. Tester le service de scoring
echo "Test du service de scoring..."
curl -s http://localhost:5000/health

# 6. Créer un workflow de test
echo "Création d'un workflow de test..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer secret" \
  -d '{
    "workflowDefinitionId": "credit_scoring_workflow",
    "context": {
      "entity": {
        "ballerineEntityId": "test-entity-001",
        "data": {
          "personalInfo": {
            "age": 35,
            "income": 50000,
            "employmentStatus": "EMPLOYED"
          },
          "financialInfo": {
            "creditHistory": 720,
            "loanAmount": 15000
          }
        }
      }
    }
  }' \
  http://localhost:3000/api/v1/workflows

echo -e "\nEnvironnement de test démarré et testé!"