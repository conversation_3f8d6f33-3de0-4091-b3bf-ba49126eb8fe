"""
Script principal pour générer le dataset synthétique tunisien complet
Génère 100k clients avec toutes les variables nécessaires pour le scoring de crédit
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
from synthetic_data_generator import TunisianCreditDataGenerator

def main():
    """Génère et exporte le dataset complet"""

    print("=" * 60)
    print("GÉNÉRATION DATASET SYNTHÉTIQUE TUNISIEN")
    print("=" * 60)
    print(f"Début de génération: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Initialiser le générateur pour 100k clients
    generator = TunisianCreditDataGenerator(n_clients=100000, random_seed=42)

    # Générer le dataset complet
    print("\n🔄 Génération en cours...")
    df = generator.generate_full_dataset()

    # Statistiques du dataset
    print("\n📊 STATISTIQUES DU DATASET")
    print("-" * 40)
    print(f"Nombre total de clients: {len(df):,}")
    print(f"Nombre de colonnes: {len(df.columns)}")
    print(f"Taille mémoire: {df.memory_usage(deep=True).sum() / 1024**2:.1f} MB")

    # Répartitions principales
    print(f"\n📈 RÉPARTITIONS PRINCIPALES")
    print("-" * 40)

    print("Décisions de crédit:")
    decisions = df['decision_finale'].value_counts()
    for decision, count in decisions.items():
        pct = count / len(df) * 100
        print(f"  {decision}: {count:,} ({pct:.1f}%)")

    print("\nClasses de risque BCT:")
    classes = df['classe_risque'].value_counts().sort_index()
    for classe, count in classes.items():
        pct = count / len(df) * 100
        print(f"  {classe}: {count:,} ({pct:.1f}%)")

    print("\nRépartition par région:")
    regions = df['region'].value_counts()
    for region, count in regions.items():
        pct = count / len(df) * 100
        print(f"  {region}: {count:,} ({pct:.1f}%)")

    print("\nTypes de crédit demandés:")
    types_credit = df['type_credit'].value_counts()
    for type_credit, count in types_credit.items():
        pct = count / len(df) * 100
        print(f"  {type_credit}: {count:,} ({pct:.1f}%)")

    # Statistiques financières
    print(f"\n💰 STATISTIQUES FINANCIÈRES")
    print("-" * 40)
    print(f"Revenu mensuel moyen: {df['revenu_mensuel'].mean():,.0f} TND")
    print(f"Revenu mensuel médian: {df['revenu_mensuel'].median():,.0f} TND")
    print(f"Montant demandé moyen: {df['montant_demande'].mean():,.0f} TND")
    print(f"Ratio d'endettement moyen: {df['ratio_endettement'].mean():.2%}")
    print(f"Score PD moyen: {df['score_pd'].mean():.3f}")

    # Validation de la qualité des données
    print(f"\n✅ VALIDATION QUALITÉ")
    print("-" * 40)

    # Vérifier les valeurs manquantes
    missing_values = df.isnull().sum().sum()
    print(f"Valeurs manquantes: {missing_values}")

    # Vérifier les doublons
    duplicates = df.duplicated().sum()
    print(f"Doublons: {duplicates}")

    # Vérifier les valeurs aberrantes
    negative_revenues = (df['revenu_mensuel'] < 0).sum()
    print(f"Revenus négatifs: {negative_revenues}")

    high_ratios = (df['ratio_endettement'] > 1).sum()
    print(f"Ratios d'endettement > 100%: {high_ratios}")

    # Cohérence des données
    inconsistent_owners = ((df['type_logement'] == 'proprietaire') &
                          (df['valeur_immobilier'] == 0)).sum()
    print(f"Propriétaires sans immobilier: {inconsistent_owners}")

    # Export du dataset
    print(f"\n💾 EXPORT DU DATASET")
    print("-" * 40)

    # Créer le dossier de sortie
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)

    # Export CSV principal
    csv_filename = f"{output_dir}/tunisian_credit_dataset_100k.csv"
    print(f"Export CSV: {csv_filename}")
    df.to_csv(csv_filename, index=False, encoding='utf-8')

    # Export échantillon pour validation
    sample_filename = f"{output_dir}/tunisian_credit_sample_1k.csv"
    print(f"Export échantillon: {sample_filename}")
    df.sample(1000, random_state=42).to_csv(sample_filename, index=False, encoding='utf-8')

    # Export par classe de risque pour analyse
    for classe in df['classe_risque'].unique():
        classe_df = df[df['classe_risque'] == classe]
        classe_filename = f"{output_dir}/tunisian_credit_classe_{classe}.csv"
        print(f"Export classe {classe}: {classe_filename} ({len(classe_df):,} clients)")
        classe_df.to_csv(classe_filename, index=False, encoding='utf-8')

    # Métadonnées
    metadata = {
        'generation_date': datetime.now().isoformat(),
        'total_clients': len(df),
        'columns': len(df.columns),
        'memory_mb': df.memory_usage(deep=True).sum() / 1024**2,
        'decisions': decisions.to_dict(),
        'risk_classes': classes.to_dict(),
        'regions': regions.to_dict(),
        'credit_types': types_credit.to_dict(),
        'avg_revenue': float(df['revenu_mensuel'].mean()),
        'avg_amount': float(df['montant_demande'].mean()),
        'avg_pd_score': float(df['score_pd'].mean()),
        'data_quality': {
            'missing_values': int(missing_values),
            'duplicates': int(duplicates),
            'negative_revenues': int(negative_revenues),
            'high_debt_ratios': int(high_ratios)
        }
    }

    import json
    metadata_filename = f"{output_dir}/dataset_metadata.json"
    print(f"Export métadonnées: {metadata_filename}")
    with open(metadata_filename, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, indent=2, ensure_ascii=False)

    # Rapport de génération
    report_filename = f"{output_dir}/generation_report.txt"
    print(f"Rapport de génération: {report_filename}")

    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write("RAPPORT DE GÉNÉRATION - DATASET SYNTHÉTIQUE TUNISIEN\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Date de génération: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Nombre de clients: {len(df):,}\n")
        f.write(f"Nombre de colonnes: {len(df.columns)}\n\n")

        f.write("COLONNES GÉNÉRÉES:\n")
        f.write("-" * 20 + "\n")
        for i, col in enumerate(df.columns, 1):
            f.write(f"{i:2d}. {col}\n")

        f.write(f"\nSTATISTIQUES PRINCIPALES:\n")
        f.write("-" * 25 + "\n")
        f.write(f"Revenu moyen: {df['revenu_mensuel'].mean():,.0f} TND\n")
        f.write(f"Montant moyen demandé: {df['montant_demande'].mean():,.0f} TND\n")
        f.write(f"Score PD moyen: {df['score_pd'].mean():.3f}\n")
        f.write(f"Taux d'approbation: {(decisions.get('APPROVE', 0) / len(df) * 100):.1f}%\n")
        f.write(f"Taux de rejet: {(decisions.get('REJECT', 0) / len(df) * 100):.1f}%\n")

    print(f"\n🎉 GÉNÉRATION TERMINÉE AVEC SUCCÈS!")
    print(f"Fin de génération: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Fichiers générés dans le dossier: {output_dir}/")

if __name__ == "__main__":
    main()
