name: New Deploy workflows-service image

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: 'Choose Environment'
        required: true
        default: 'dev'
        options:
          - 'sb'
          - 'prod'

        
  workflow_call:
    inputs:
      environment:
        type: string
        description: 'Environment'
        required: true
        default: 'dev'
      sha:
        type: string
        description: 'SHA ID'
        required: true


env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository_owner }}/workflows-service
  SHORT_HASH: ${{ (inputs.environment == 'dev' && inputs.sha) || (inputs.environment == 'prod' && vars.SB_WF_SHORT_SHA) || (vars.DEV_WF_SHORT_SHA) }}

jobs:
  set_short_hash:
    runs-on: ubuntu-latest
    steps:
      - name: Verify SHORT_HASH
        run: |
          echo "SHORT_HASH is ${{ env.SHORT_HASH }}"
          echo "SHORT_HASH is ${{ env.SHORT_HASH }}"
          echo "SHORT_HASH is $SHORT_HASH"

  tag-and-push-image:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Get version
        id: version
        run: |
          echo "sha_short=${{ env.SHORT_HASH }}" >> $GITHUB_OUTPUT
          if [ "${{ inputs.environment }}" == "prod" ]; then
          echo "PROD_WF_SHORT_SHA=${{ env.SHORT_HASH }}" >> $GITHUB_ENV
          else
          echo "SB_WF_SHORT_SHA=${{ env.SHORT_HASH }}" >> $GITHUB_ENV
          fi

      - name: Update Service version in Environment
        if: ${{ inputs.environment != 'dev' }}
        run: |
          if [ "${{ inputs.environment }}" == "prod" ]; then
            ENV="PROD"
          elif [ "${{ inputs.environment }}" == "sb" ]; then
            ENV="SB"
          else
            ENV="DEV"
          fi
          echo "$ENV"
          curl -X PATCH \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer ${{ secrets.GH_CI_ENV_TOKEN }}" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            "https://api.github.com/repos/ballerine-io/ballerine/actions/variables/${ENV^^}_WF_SHORT_SHA" \
            -d "{\"name\":\"${ENV}_WF_SHORT_SHA\",\"value\":\"${{ env.SHORT_HASH }}\"}"

      - name: Cache Docker layers
        id: cache
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-docker-${{ hashFiles('**/Dockerfile') }}
          restore-keys: |
            ${{ runner.os }}-docker-${{ hashFiles('**/Dockerfile') }}
            ${{ runner.os }}-docker-
  
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
        with:
          platforms: 'arm64,arm'

      - name: Log in to the container registry
        uses: docker/login-action@65b78e6e13532edd9afa3aa52ac7964289d1a9c1
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Pull and Tag Existing Image
        if: ${{ inputs.environment != 'dev' }}
        run: |
          if [ "${{ inputs.environment }}" == "prod" ]; then
            docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:${{ env.SHORT_HASH }}-sb
            docker tag ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:${{ env.SHORT_HASH }}-sb ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:${{ env.SHORT_HASH }}-${{ inputs.environment }}
            docker tag ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:${{ env.SHORT_HASH }}-sb ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:${{ inputs.environment }}
            docker tag ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:${{ env.SHORT_HASH }}-sb ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:latest
            docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:latest
          else
            docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:${{ env.SHORT_HASH }}-dev
            docker tag ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:${{ env.SHORT_HASH }}-dev ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:${{ env.SHORT_HASH }}-${{ inputs.environment }}
            docker tag ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:${{ env.SHORT_HASH }}-dev ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:${{ inputs.environment }}
          fi
          docker images
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:${{ env.SHORT_HASH }}-${{ inputs.environment }}
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}:${{ inputs.environment }}

  build-and-push-ee-image:
    runs-on: ubuntu-latest
    needs: [tag-and-push-image]
    outputs:
      SUBMODULE_SHORT_HASH: ${{ steps.lastcommit.outputs.shorthash }}
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      
      - name: Checkout wf-data-migration
        uses: actions/checkout@v4
        with:
          repository: ballerine-io/wf-data-migration
          token: ${{ secrets.SUBMODULES_TOKEN }}
          ref: ${{ inputs.environment }}
          fetch-depth: 1
          path: services/workflows-service/prisma/data-migrations

      - name: Get Latest Commit ID
        id: lastcommit
        uses: nmbgeek/github-action-get-latest-commit@main
        with:
          owner: ${{ github.repository_owner }}
          token: ${{ secrets.SUBMODULES_TOKEN }}
          repo: wf-data-migration
          branch: ${{ inputs.environment }}

      - name: Set Commit Id as Env 
        run: echo "SUBMODULE_SHORT_HASH=${{ steps.lastcommit.outputs.shorthash }}" >> $GITHUB_ENV
      
      - name: Cache Docker layers
        id: cache
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-docker-${{ hashFiles('**/Dockerfile') }}
          restore-keys: |
            ${{ runner.os }}-docker-${{ hashFiles('**/Dockerfile') }}
            ${{ runner.os }}-docker-

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
        with:
          platforms: 'arm64,arm'

      - name: Log in to the container registry
        uses: docker/login-action@65b78e6e13532edd9afa3aa52ac7964289d1a9c1
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata for ee Docker images
        id: eemeta
        uses: docker/metadata-action@9ec57ed1fcdbf14dcef7dfbe97b2010124a938b7
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME}}-ee
          tags: |
            type=raw,value=${{ inputs.environment }}
            type=raw,value=${{ env.SHORT_HASH }}-${{ steps.lastcommit.outputs.shorthash }}-${{ inputs.environment }}
            type=raw,value=latest,enable=${{ inputs.environment == 'prod' }}
            type=sha,format=short
            
      - name: Build and push ee Docker image
        uses: docker/build-push-action@v5
        with:
          context: services/workflows-service
          file: services/workflows-service/Dockerfile.ee
          platforms: linux/amd64
          push: true
          cache-from: type=local,src=/tmp/.buildx-cache
          tags: ${{ steps.eemeta.outputs.tags }}
          build-args: |
            "BASE_IMAGE=ghcr.io/${{ github.repository_owner }}/workflows-service:${{ env.SHORT_HASH }}-${{ inputs.environment }}"
  
  update-helm-chart:
    runs-on: ubuntu-latest
    needs: build-and-push-ee-image
    permissions:
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Checkout cloud-infra-config repository
        uses: actions/checkout@v4
        with:
          repository: ballerine-io/cloud-infra-config
          token: ${{ secrets.GIT_TOKEN }}
          ref: main
          fetch-depth: 1
          sparse-checkout: |
            kubernetes/helm/wf-service
          sparse-checkout-cone-mode: true
      - name: Check if values yaml file exists 
        id: update_helm_check
        shell: bash
        run: |
          if [ -f "kubernetes/helm/wf-service/${{ inputs.environment }}-custom-values.yaml" ]; then
            echo "file_name=${{ inputs.environment }}-custom-values.yaml" >> "$GITHUB_OUTPUT"
            echo ${{ env.SHORT_HASH }}-${{ needs.build-and-push-ee-image.outputs.SUBMODULE_SHORT_HASH }}
          else
            echo "file_name=dev-custom-values.yaml" >> "$GITHUB_OUTPUT"
            echo ${{ env.SHORT_HASH }}-${{ needs.build-and-push-ee-image.outputs.SUBMODULE_SHORT_HASH }}
          fi
            
      - name: Update workflow-service image version in the HelmChart
        uses: fjogeleit/yaml-update-action@main
        with:
          repository: ballerine-io/cloud-infra-config
          branch: main
          commitChange: true
          message: "Update ${{ inputs.environment }} wf-service image Version to ${{ env.SHORT_HASH }}-${{ needs.build-and-push-ee-image.outputs.SUBMODULE_SHORT_HASH }}-${{ inputs.environment }} - (Commit hash: ${{ github.sha }}, commit message: ${{ github.event.head_commit.message }})"
          token: ${{ secrets.GIT_TOKEN }}
          changes: |
            {
              "kubernetes/helm/wf-service/${{steps.update_helm_check.outputs.file_name}}": {
                "image.tag": "${{ env.SHORT_HASH }}-${{ needs.build-and-push-ee-image.outputs.SUBMODULE_SHORT_HASH }}-${{ inputs.environment }}",
                "prismaMigrate.image.tag": "${{ env.SHORT_HASH }}-${{ needs.build-and-push-ee-image.outputs.SUBMODULE_SHORT_HASH }}-${{ inputs.environment }}",
                "dbMigrate.image.tag": "${{ env.SHORT_HASH }}-${{ needs.build-and-push-ee-image.outputs.SUBMODULE_SHORT_HASH }}-${{ inputs.environment }}",
                "dataSync.image.tag": "${{ env.SHORT_HASH }}-${{ needs.build-and-push-ee-image.outputs.SUBMODULE_SHORT_HASH }}-${{ inputs.environment }}"
              }
            }
  send-to-slack:
    runs-on: ubuntu-latest
    needs: [update-helm-chart,build-and-push-ee-image]
    if: ${{ needs.update-helm-chart.result == 'success' }}
    environment: ${{ inputs.environment }}
    permissions:
      contents: read
      packages: write

    steps:
      - name: Send alert to Slack channel
        id: slack
        uses: slackapi/slack-github-action@v1.26.0
        with:
          channel-id: '${{ secrets.ARGO_SLACK_CHANNEL_ID }}'
          slack-message: "Test Wf-service Deployment in ${{ inputs.environment }} with tag ${{ env.SHORT_HASH }}-${{ needs.build-and-push-ee-image.outputs.SUBMODULE_SHORT_HASH }}-${{ inputs.environment }} build result: ${{ job.status }}. successfully updated the wf-service helm values for ${{ inputs.environment }}."
        env:
          SLACK_BOT_TOKEN: ${{ secrets.ARGO_SLACK_BOT_TOKEN }}

