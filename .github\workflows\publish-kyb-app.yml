name: Publish kyb-app image

on:
  workflow_dispatch:
  push:
    # Run this pipeline only if there are changes in specified path
    paths:
      - 'apps/kyb-app/**'
    branches:
      - dev
      - test
      - prod
      - staging
      - sb
      - demo

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository_owner }}/kyb-app

jobs:
  build-and-push-image:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Install jq
        run: sudo apt-get install jq

      - name: Log in to the Container registry
        uses: docker/login-action@65b78e6e13532edd9afa3aa52ac7964289d1a9c1
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Get package version from package.json
        id: get_version
        run: |
          PACKAGE_VERSION=$(jq -r '.version' apps/kyb-app/package.json)
          echo "::set-output name=version::$PACKAGE_VERSION"

      - name: Print the version
        run: echo "The version was ${{ steps.get_version.outputs.version }}"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
        with:
          platforms: 'arm64,arm'

      - name: Log in to the Container registry
        uses: docker/login-action@65b78e6e13532edd9afa3aa52ac7964289d1a9c1
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata for non Prod Docker images
        if: github.ref != 'refs/heads/prod'
        id: branchmeta
        uses: docker/metadata-action@9ec57ed1fcdbf14dcef7dfbe97b2010124a938b7
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=raw,value=${{ github.head_ref || github.ref_name }}
            type=raw,value=commit-${{ github.sha }}-${{ github.head_ref || github.ref_name }}
            type=raw,value=${{ steps.get_version.outputs.version }}-${{ github.head_ref || github.ref_name }}

      - name: Build and push Docker image for non Prod
        if: github.ref != 'refs/heads/prod'
        uses: docker/build-push-action@v4
        with:
          context: apps/kyb-app
          platforms: linux/amd64
          push: true
          cache-from: '${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.head_ref || github.ref_name }}'
          tags: ${{ steps.branchmeta.outputs.tags }}

      - name: Extract metadata (tags, labels) for prod Docker images
        if: github.ref == 'refs/heads/prod'
        # This branch will have the tag latest
        id: prodmeta
        uses: docker/metadata-action@9ec57ed1fcdbf14dcef7dfbe97b2010124a938b7
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=raw,value=prod
            type=raw,value=commit-${{ github.sha }}-prod
            type=raw,value=${{ steps.get_version.outputs.version }}-prod
            type=raw,value=latest

      - name: Build and push Docker image for Prod
        if: github.ref == 'refs/heads/prod'
        uses: docker/build-push-action@v4
        with:
          context: apps/kyb-app
          platforms: linux/amd64
          push: true
          cache-from: '${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.head_ref || github.ref_name }}'
          tags: ${{ steps.prodmeta.outputs.tags }}

      - name: Scan Docker Image
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: '${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.head_ref || github.ref_name }}'
          format: 'table'
          ignore-unfixed: true
          exit-code: 1
          vuln-type: 'os,library'
          severity: 'CRITICAL'
