"""
Script pour générer un dataset de 10k clients pour test rapide
"""

from synthetic_data_generator import TunisianCreditDataGenerator
import pandas as pd
from datetime import datetime

def main():
    print("🚀 GÉNÉRATION DATASET TEST (10k clients)")
    print("=" * 50)
    
    start_time = datetime.now()
    
    # Générer 10k clients
    generator = TunisianCreditDataGenerator(n_clients=10000, random_seed=42)
    df = generator.generate_full_dataset()
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print(f"\n✅ Génération terminée en {duration:.1f} secondes")
    print(f"📊 Dataset: {len(df)} clients, {len(df.columns)} colonnes")
    
    # Statistiques rapides
    print(f"\n📈 STATISTIQUES RAPIDES:")
    print(f"  • Décisions APPROVE: {(df['decision_finale'] == 'APPROVE').sum():,} ({(df['decision_finale'] == 'APPROVE').mean()*100:.1f}%)")
    print(f"  • Décisions REJECT: {(df['decision_finale'] == 'REJECT').sum():,} ({(df['decision_finale'] == 'REJECT').mean()*100:.1f}%)")
    print(f"  • Score PD moyen: {df['score_pd'].mean():.3f}")
    print(f"  • Revenu médian: {df['revenu_mensuel'].median():.0f} TND")
    
    # Export
    filename = "output/tunisian_credit_10k_test.csv"
    df.to_csv(filename, index=False, encoding='utf-8')
    print(f"\n💾 Exporté vers: {filename}")
    
    # Estimation pour 100k
    estimated_time_100k = duration * 10
    print(f"\n⏱️ Temps estimé pour 100k clients: {estimated_time_100k/60:.1f} minutes")

if __name__ == "__main__":
    main()
