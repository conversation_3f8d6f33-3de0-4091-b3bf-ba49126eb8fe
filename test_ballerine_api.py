#!/usr/bin/env python3
"""
Test complet de l'API Ballerine en fonctionnement
"""

import requests
import json
import time

BASE_URL = "http://localhost:5001"

def test_health():
    """Test du health check"""
    print("🔍 Test du health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Service healthy - Modèle entraîné: {data['model_trained']}")
            return True
        else:
            print(f"❌ Health check échoué - Code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur health check: {e}")
        return False

def test_model_info():
    """Test des informations du modèle"""
    print("\n📊 Test des informations du modèle...")
    try:
        response = requests.get(f"{BASE_URL}/model/info")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Modèle: {data['model_type']}")
            print(f"   Features: {data['features_count']}")
            print(f"   Classes: {', '.join(data['classes'])}")
            return True
        else:
            print(f"❌ Info modèle échoué - Code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur info modèle: {e}")
        return False

def test_scoring_scenarios():
    """Test de différents scénarios de scoring"""
    print("\n🎯 Test des scénarios de scoring...")
    
    scenarios = [
        {
            "name": "Client à faible risque",
            "data": {
                "age": 35,
                "sexe": 1,
                "nombre_enfants": 2,
                "anciennete_emploi": 8,
                "revenu_mensuel": 2500,
                "ratio_endettement": 15.0,
                "score_comportement": 0.95,
                "nombre_incidents_12m": 0,
                "anciennete_relation_bancaire": 10,
                "taux_utilisation_credit": 0.2
            }
        },
        {
            "name": "Client à risque moyen",
            "data": {
                "age": 28,
                "sexe": 0,
                "nombre_enfants": 1,
                "anciennete_emploi": 3,
                "revenu_mensuel": 1200,
                "ratio_endettement": 35.0,
                "score_comportement": 0.75,
                "nombre_incidents_12m": 1,
                "anciennete_relation_bancaire": 3,
                "taux_utilisation_credit": 0.6
            }
        },
        {
            "name": "Client à haut risque",
            "data": {
                "age": 22,
                "sexe": 1,
                "nombre_enfants": 0,
                "anciennete_emploi": 1,
                "revenu_mensuel": 800,
                "ratio_endettement": 65.0,
                "score_comportement": 0.45,
                "nombre_incidents_12m": 3,
                "anciennete_relation_bancaire": 1,
                "taux_utilisation_credit": 0.9
            }
        }
    ]
    
    results = []
    
    for scenario in scenarios:
        print(f"\n📋 {scenario['name']}:")
        try:
            response = requests.post(
                f"{BASE_URL}/score",
                json=scenario['data'],
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                classe = result.get('classe_risque', 'N/A')
                confidence = result.get('confidence', 0)
                
                print(f"   ✅ Classe de risque: {classe}")
                print(f"   📊 Confiance: {confidence:.3f}")
                
                # Affichage des probabilités
                if 'probabilities' in result:
                    probs = result['probabilities']
                    top_classes = sorted(probs.items(), key=lambda x: x[1], reverse=True)[:3]
                    print(f"   🎯 Top 3 probabilités:")
                    for cls, prob in top_classes:
                        print(f"      {cls}: {prob:.3f}")
                
                results.append({
                    'scenario': scenario['name'],
                    'classe': classe,
                    'confidence': confidence,
                    'success': True
                })
                
            else:
                print(f"   ❌ Erreur - Code: {response.status_code}")
                print(f"   📝 Réponse: {response.text}")
                results.append({
                    'scenario': scenario['name'],
                    'success': False,
                    'error': response.status_code
                })
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            results.append({
                'scenario': scenario['name'],
                'success': False,
                'error': str(e)
            })
    
    return results

def test_performance():
    """Test de performance avec plusieurs requêtes"""
    print("\n⚡ Test de performance...")
    
    test_data = {
        "age": 30,
        "sexe": 1,
        "revenu_mensuel": 1500,
        "ratio_endettement": 25.0,
        "score_comportement": 0.8
    }
    
    num_requests = 10
    start_time = time.time()
    
    successes = 0
    for i in range(num_requests):
        try:
            response = requests.post(f"{BASE_URL}/score", json=test_data)
            if response.status_code == 200:
                successes += 1
        except:
            pass
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"✅ {successes}/{num_requests} requêtes réussies")
    print(f"⏱️ Temps total: {duration:.3f} secondes")
    print(f"🚀 Débit: {successes/duration:.1f} requêtes/seconde")
    
    return successes == num_requests

def main():
    """Fonction principale de test"""
    print("🧪 TEST COMPLET DE L'API BALLERINE")
    print("=" * 50)
    
    # Tests séquentiels
    tests = [
        ("Health Check", test_health),
        ("Informations Modèle", test_model_info),
        ("Scénarios de Scoring", test_scoring_scenarios),
        ("Performance", test_performance)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔄 {test_name}...")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results[test_name] = False
    
    # Résumé final
    print("\n" + "=" * 50)
    print("📋 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    for test_name, result in results.items():
        if test_name == "Scénarios de Scoring":
            if isinstance(result, list):
                success_count = sum(1 for r in result if r.get('success', False))
                total_count = len(result)
                status = "✅" if success_count == total_count else "⚠️"
                print(f"{status} {test_name}: {success_count}/{total_count} scénarios réussis")
            else:
                print(f"❌ {test_name}: Erreur")
        else:
            status = "✅" if result else "❌"
            print(f"{status} {test_name}")
    
    print("\n🎉 Tests terminés!")
    print("🌐 Ballerine est opérationnel sur http://localhost:5001")

if __name__ == "__main__":
    main()
