"""
Script pour générer le dataset complet avec les 3 catégories de clients:
- Tunisiens résidents en Tunisie
- Tunisiens résidents à l'étranger (TRE)
- Étrangers résidents en Tunisie (ERT)
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
from synthetic_data_generator import TunisianCreditDataGenerator

def main():
    """Génère et exporte le dataset complet multicatégories"""
    
    print("=" * 70)
    print("GÉNÉRATION DATASET SYNTHÉTIQUE TUNISIEN MULTICATÉGORIES")
    print("Tunisiens résidents + TRE + ERT")
    print("=" * 70)
    print(f"Début de génération: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Initialiser le générateur pour 100k clients
    generator = TunisianCreditDataGenerator(n_clients=100000, random_seed=42)
    
    # Générer le dataset complet
    print("\n🔄 Génération en cours...")
    df = generator.generate_full_dataset()
    
    # Statistiques du dataset
    print("\n📊 STATISTIQUES DU DATASET")
    print("-" * 50)
    print(f"Nombre total de clients: {len(df):,}")
    print(f"Nombre de colonnes: {len(df.columns)}")
    print(f"Taille mémoire: {df.memory_usage(deep=True).sum() / 1024**2:.1f} MB")
    
    # Répartitions par catégorie
    print(f"\n🏷️ RÉPARTITION PAR CATÉGORIE DE CLIENT")
    print("-" * 50)
    categories = df['categorie_client'].value_counts()
    for cat, count in categories.items():
        pct = count / len(df) * 100
        description = {
            'tunisien_resident': 'Tunisiens résidents en Tunisie',
            'tunisien_resident_etranger': 'Tunisiens résidents à l\'étranger (TRE)',
            'etranger_resident_tunisie': 'Étrangers résidents en Tunisie (ERT)'
        }
        print(f"  {description[cat]}")
        print(f"    {count:,} clients ({pct:.1f}%)")
    
    # Revenus par catégorie
    print(f"\n💰 REVENUS MOYENS PAR CATÉGORIE")
    print("-" * 50)
    for cat in df['categorie_client'].unique():
        cat_df = df[df['categorie_client'] == cat]
        revenu_moyen = cat_df['revenu_mensuel'].mean()
        revenu_median = cat_df['revenu_mensuel'].median()
        print(f"  {cat}:")
        print(f"    Moyenne: {revenu_moyen:,.0f} TND")
        print(f"    Médiane: {revenu_median:,.0f} TND")
    
    # Analyse TRE par pays
    print(f"\n🌍 TUNISIENS RÉSIDENTS À L'ÉTRANGER (TRE)")
    print("-" * 50)
    tre_df = df[df['categorie_client'] == 'tunisien_resident_etranger']
    if len(tre_df) > 0:
        pays_stats = tre_df.groupby('pays_residence').agg({
            'client_id': 'count',
            'revenu_mensuel': ['mean', 'median']
        }).round(0)
        
        for pays in tre_df['pays_residence'].unique():
            pays_df = tre_df[tre_df['pays_residence'] == pays]
            count = len(pays_df)
            pct = count / len(tre_df) * 100
            revenu_moyen = pays_df['revenu_mensuel'].mean()
            print(f"  {pays.capitalize():<15}: {count:>5,} clients ({pct:>5.1f}%) - {revenu_moyen:>8,.0f} TND")
    
    # Analyse ERT par nationalité
    print(f"\n🏛️ ÉTRANGERS RÉSIDENTS EN TUNISIE (ERT)")
    print("-" * 50)
    ert_df = df[df['categorie_client'] == 'etranger_resident_tunisie']
    if len(ert_df) > 0:
        for nat in ert_df['nationalite'].unique():
            nat_df = ert_df[ert_df['nationalite'] == nat]
            count = len(nat_df)
            pct = count / len(ert_df) * 100
            revenu_moyen = nat_df['revenu_mensuel'].mean()
            print(f"  {nat.capitalize():<15}: {count:>5,} clients ({pct:>5.1f}%) - {revenu_moyen:>8,.0f} TND")
    
    # Décisions par catégorie
    print(f"\n⚖️ DÉCISIONS DE CRÉDIT PAR CATÉGORIE")
    print("-" * 50)
    for cat in df['categorie_client'].unique():
        cat_df = df[df['categorie_client'] == cat]
        decisions = cat_df['decision_finale'].value_counts()
        print(f"\n  {cat}:")
        for decision, count in decisions.items():
            pct = count / len(cat_df) * 100
            print(f"    {decision:<15}: {count:>6,} ({pct:>5.1f}%)")
    
    # Classes de risque par catégorie
    print(f"\n🏦 CLASSES DE RISQUE BCT PAR CATÉGORIE")
    print("-" * 50)
    for cat in df['categorie_client'].unique():
        cat_df = df[df['categorie_client'] == cat]
        classes = cat_df['classe_risque'].value_counts().sort_index()
        print(f"\n  {cat}:")
        for classe, count in classes.items():
            pct = count / len(cat_df) * 100
            print(f"    {classe}: {count:>6,} ({pct:>5.1f}%)")
    
    # Export du dataset
    print(f"\n💾 EXPORT DU DATASET")
    print("-" * 50)
    
    # Créer le dossier de sortie
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)
    
    # Export CSV principal
    csv_filename = f"{output_dir}/tunisian_credit_multicategories_100k.csv"
    print(f"Export principal: {csv_filename}")
    df.to_csv(csv_filename, index=False, encoding='utf-8')
    
    # Export par catégorie
    for cat in df['categorie_client'].unique():
        cat_df = df[df['categorie_client'] == cat]
        cat_filename = f"{output_dir}/tunisian_credit_{cat}.csv"
        print(f"Export {cat}: {cat_filename} ({len(cat_df):,} clients)")
        cat_df.to_csv(cat_filename, index=False, encoding='utf-8')
    
    # Export TRE par pays
    tre_df = df[df['categorie_client'] == 'tunisien_resident_etranger']
    if len(tre_df) > 0:
        for pays in tre_df['pays_residence'].unique():
            pays_df = tre_df[tre_df['pays_residence'] == pays]
            if len(pays_df) >= 100:  # Seulement si assez de données
                pays_filename = f"{output_dir}/tunisian_credit_tre_{pays}.csv"
                print(f"Export TRE {pays}: {pays_filename} ({len(pays_df):,} clients)")
                pays_df.to_csv(pays_filename, index=False, encoding='utf-8')
    
    # Export ERT par nationalité
    ert_df = df[df['categorie_client'] == 'etranger_resident_tunisie']
    if len(ert_df) > 0:
        for nat in ert_df['nationalite'].unique():
            nat_df = ert_df[ert_df['nationalite'] == nat]
            if len(nat_df) >= 50:  # Seulement si assez de données
                nat_filename = f"{output_dir}/tunisian_credit_ert_{nat}.csv"
                print(f"Export ERT {nat}: {nat_filename} ({len(nat_df):,} clients)")
                nat_df.to_csv(nat_filename, index=False, encoding='utf-8')
    
    # Métadonnées étendues
    metadata = {
        'generation_date': datetime.now().isoformat(),
        'total_clients': len(df),
        'columns': len(df.columns),
        'memory_mb': df.memory_usage(deep=True).sum() / 1024**2,
        'categories': {
            cat: {
                'count': int(count),
                'percentage': float(count / len(df) * 100),
                'avg_revenue': float(df[df['categorie_client'] == cat]['revenu_mensuel'].mean())
            }
            for cat, count in categories.items()
        },
        'tre_countries': {
            pays: {
                'count': int(len(tre_df[tre_df['pays_residence'] == pays])),
                'avg_revenue': float(tre_df[tre_df['pays_residence'] == pays]['revenu_mensuel'].mean())
            }
            for pays in tre_df['pays_residence'].unique()
        } if len(tre_df) > 0 else {},
        'ert_nationalities': {
            nat: {
                'count': int(len(ert_df[ert_df['nationalite'] == nat])),
                'avg_revenue': float(ert_df[ert_df['nationalite'] == nat]['revenu_mensuel'].mean())
            }
            for nat in ert_df['nationalite'].unique()
        } if len(ert_df) > 0 else {}
    }
    
    import json
    metadata_filename = f"{output_dir}/dataset_multicategories_metadata.json"
    print(f"Export métadonnées: {metadata_filename}")
    with open(metadata_filename, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 GÉNÉRATION MULTICATÉGORIES TERMINÉE AVEC SUCCÈS!")
    print(f"Fin de génération: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Fichiers générés dans le dossier: {output_dir}/")
    
    # Résumé final
    print(f"\n📋 RÉSUMÉ FINAL:")
    print(f"  • {categories['tunisien_resident']:,} Tunisiens résidents")
    print(f"  • {categories['tunisien_resident_etranger']:,} Tunisiens résidents à l'étranger")
    print(f"  • {categories['etranger_resident_tunisie']:,} Étrangers résidents en Tunisie")
    print(f"  • Total: {len(df):,} clients avec {len(df.columns)} variables")

if __name__ == "__main__":
    main()
