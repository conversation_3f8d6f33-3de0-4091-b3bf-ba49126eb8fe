"""
Générateur de données synthétiques pour le scoring de crédit tunisien
Génère 100k clients avec corrélations réalistes selon le marché tunisien
"""

import pandas as pd
import numpy as np
import random
from datetime import datetime, timedelta
from typing import Dict, List, <PERSON><PERSON>
import uuid

from tunisian_characteristics import (
    SECTEURS_ECONOMIQUES, PROFESSIONS_PAR_SECTEUR, REGIONS_TUNISIENNES,
    SITUATIONS_FAMILIALES, NIVEAUX_EDUCATION, TYPES_LOGEMENT,
    BANQUES_TUNISIENNES, CLASSES_RISQUE_BCT, TAUX_CREDIT_TUNISIE,
    CATEGORIES_CLIENTS, PAYS_RESIDENCE_TRE, NATIONALITES_ERT
)

class TunisianCreditDataGenerator:
    """Générateur de données synthétiques pour clients tunisiens"""

    def __init__(self, n_clients: int = 100000, random_seed: int = 42):
        self.n_clients = n_clients
        np.random.seed(random_seed)
        random.seed(random_seed)

    def generate_client_profile(self) -> Dict:
        """Génère le profil démographique d'un client"""
        # Âge avec distribution réaliste (25-65 ans, pic à 35-45)
        age = int(np.random.beta(2, 3) * 40 + 25)

        # Sexe
        sexe = np.random.choice(['M', 'F'], p=[0.52, 0.48])

        # Catégorie de client
        categories = list(CATEGORIES_CLIENTS.keys())
        probs = [CATEGORIES_CLIENTS[c]['proportion'] for c in categories]
        categorie_client = np.random.choice(categories, p=probs)

        # Nationalité et pays de résidence selon la catégorie
        if categorie_client == 'tunisien_resident':
            nationalite = 'tunisienne'
            pays_residence = 'tunisie'
        elif categorie_client == 'tunisien_resident_etranger':
            nationalite = 'tunisienne'
            pays_list = list(PAYS_RESIDENCE_TRE.keys())
            pays_probs = [PAYS_RESIDENCE_TRE[p]['proportion'] for p in pays_list]
            pays_residence = np.random.choice(pays_list, p=pays_probs)
        else:  # etranger_resident_tunisie
            pays_residence = 'tunisie'
            nat_list = list(NATIONALITES_ERT.keys())
            nat_probs = [NATIONALITES_ERT[n]['proportion'] for n in nat_list]
            nationalite = np.random.choice(nat_list, p=nat_probs)

        # Région avec distribution démographique tunisienne
        regions = list(REGIONS_TUNISIENNES.keys())
        probs = [REGIONS_TUNISIENNES[r]['population_pct'] for r in regions]
        region = np.random.choice(regions, p=probs)

        # Situation familiale corrélée à l'âge
        if age < 30:
            situation_probs = [0.6, 0.25, 0.10, 0.03, 0.01, 0.01]
        elif age < 45:
            situation_probs = [0.2, 0.15, 0.35, 0.25, 0.03, 0.02]
        else:
            situation_probs = [0.15, 0.10, 0.25, 0.35, 0.10, 0.05]

        situations = list(SITUATIONS_FAMILIALES.keys())
        situation_familiale = np.random.choice(situations, p=situation_probs)

        # Nombre d'enfants corrélé à la situation familiale et l'âge
        if 'sans_enfants' in situation_familiale or situation_familiale == 'celibataire':
            nombre_enfants = 0
        elif '1_2_enfants' in situation_familiale:
            nombre_enfants = np.random.choice([1, 2], p=[0.6, 0.4])
        elif '3_plus_enfants' in situation_familiale:
            nombre_enfants = np.random.choice([3, 4, 5], p=[0.5, 0.3, 0.2])
        else:
            nombre_enfants = np.random.poisson(1.5)

        # Niveau d'éducation corrélé à l'âge et la région
        education_boost = 1.2 if region in ['tunis', 'sfax'] else 0.8
        if age < 35:
            education_boost *= 1.3  # Jeunes plus éduqués

        niveaux = list(NIVEAUX_EDUCATION.keys())
        probs = list(NIVEAUX_EDUCATION.values())
        # Ajuster les probabilités selon la région/âge
        probs = [p * education_boost if i >= 3 else p / education_boost
                for i, p in enumerate(probs)]
        probs = [p / sum(probs) for p in probs]  # Normaliser
        niveau_education = np.random.choice(niveaux, p=probs)

        # Secteur d'activité et profession
        secteurs = list(SECTEURS_ECONOMIQUES.keys())
        # Probabilités ajustées selon la région
        if region == 'tunis':
            secteur_probs = [0.05, 0.08, 0.12, 0.20, 0.08, 0.15, 0.10, 0.08, 0.05, 0.04, 0.15, 0.10]
        elif region in ['sfax', 'sousse']:
            secteur_probs = [0.08, 0.15, 0.18, 0.12, 0.15, 0.12, 0.08, 0.06, 0.08, 0.06, 0.08, 0.04]
        else:
            secteur_probs = [0.25, 0.12, 0.08, 0.05, 0.05, 0.15, 0.12, 0.05, 0.08, 0.15, 0.03, 0.07]

        # Normaliser les probabilités pour qu'elles somment à 1
        secteur_probs = [p / sum(secteur_probs) for p in secteur_probs]
        secteur_activite = np.random.choice(secteurs, p=secteur_probs)
        profession = np.random.choice(PROFESSIONS_PAR_SECTEUR[secteur_activite])

        # Ancienneté emploi corrélée à l'âge
        anciennete_emploi = min(int(np.random.exponential(scale=(age-22)*0.8)), (age-22)*12)
        anciennete_emploi = max(1, anciennete_emploi)

        # Type de contrat corrélé au secteur et à l'éducation
        if secteur_activite == 'fonction_publique':
            type_contrat = 'fonctionnaire'
        elif niveau_education in ['universitaire', 'post_universitaire']:
            type_contrat = np.random.choice(['CDI', 'CDD', 'freelance'], p=[0.7, 0.2, 0.1])
        else:
            type_contrat = np.random.choice(['CDI', 'CDD', 'freelance'], p=[0.5, 0.4, 0.1])

        # Type de logement corrélé à l'âge et au revenu (sera calculé plus tard)
        types = list(TYPES_LOGEMENT.keys())
        probs = list(TYPES_LOGEMENT.values())
        if age < 30:
            probs = [0.3, 0.4, 0.3]  # Plus de locataires/famille chez les jeunes
        type_logement = np.random.choice(types, p=probs)

        anciennete_logement = min(int(np.random.exponential(scale=36)), anciennete_emploi)

        return {
            'client_id': str(uuid.uuid4()),
            'cin': f"{random.randint(10000000, 99999999)}",
            'age': age,
            'sexe': sexe,
            'categorie_client': categorie_client,
            'nationalite': nationalite,
            'pays_residence': pays_residence,
            'situation_familiale': situation_familiale,
            'nombre_enfants': nombre_enfants,
            'niveau_education': niveau_education,
            'region': region,
            'profession': profession,
            'secteur_activite': secteur_activite,
            'anciennete_emploi': anciennete_emploi,
            'type_contrat': type_contrat,
            'type_logement': type_logement,
            'anciennete_logement': anciennete_logement,
            'taux_chomage_sectoriel': SECTEURS_ECONOMIQUES[secteur_activite]['taux_chomage']
        }

    def generate_financial_data(self, profile: Dict) -> Dict:
        """Génère les données financières corrélées au profil"""
        # Revenu de base selon le secteur et l'éducation
        base_salary = SECTEURS_ECONOMIQUES[profile['secteur_activite']]['salaire_moyen']

        # Ajustements selon l'éducation
        education_multiplier = {
            'primaire': 0.7,
            'secondaire': 0.9,
            'technique': 1.0,
            'universitaire': 1.4,
            'post_universitaire': 1.8
        }

        # Ajustements selon l'âge (expérience)
        age_multiplier = 0.8 + (profile['age'] - 25) * 0.02
        age_multiplier = min(age_multiplier, 1.5)

        # Ajustements selon la région
        region_multiplier = {
            'tunis': 1.3,
            'sfax': 1.1,
            'sousse': 1.0,
            'kairouan': 0.8,
            'gafsa': 0.7,
            'autres': 0.9
        }

        # Multiplicateur selon la catégorie de client
        category_multiplier = 1.0
        if profile['categorie_client'] == 'tunisien_resident_etranger':
            # TRE ont des revenus plus élevés selon le pays
            pays_data = PAYS_RESIDENCE_TRE.get(profile['pays_residence'], {'multiplicateur_revenu': 3.0})
            category_multiplier = pays_data['multiplicateur_revenu']
        elif profile['categorie_client'] == 'etranger_resident_tunisie':
            # ERT ont des revenus variables selon la nationalité
            nat_data = NATIONALITES_ERT.get(profile['nationalite'], {'multiplicateur_revenu': 1.5})
            category_multiplier = nat_data['multiplicateur_revenu']

        revenu_mensuel = base_salary * education_multiplier[profile['niveau_education']]
        revenu_mensuel *= age_multiplier * region_multiplier[profile['region']] * category_multiplier

        # Ajouter de la variabilité
        volatilite = SECTEURS_ECONOMIQUES[profile['secteur_activite']]['volatilite']
        revenu_mensuel *= np.random.normal(1.0, volatilite)
        revenu_mensuel = max(500, revenu_mensuel)  # Salaire minimum tunisien

        # Autres revenus (plus probables pour les plus éduqués/âgés)
        prob_autres_revenus = 0.1 + (profile['age'] - 25) * 0.01
        if profile['niveau_education'] in ['universitaire', 'post_universitaire']:
            prob_autres_revenus *= 2

        autres_revenus = 0
        if np.random.random() < prob_autres_revenus:
            autres_revenus = np.random.exponential(scale=revenu_mensuel * 0.3)

        revenu_total = revenu_mensuel + autres_revenus

        # Patrimoine corrélé au revenu et à l'âge
        patrimoine_multiplier = (profile['age'] - 25) * 0.1 + 1

        # Immobilier (plus probable pour les propriétaires)
        valeur_immobilier = 0
        if profile['type_logement'] == 'proprietaire':
            valeur_immobilier = revenu_mensuel * 12 * np.random.uniform(3, 8) * patrimoine_multiplier

        # Véhicule
        prob_vehicule = 0.3 + (revenu_mensuel / 3000) * 0.4
        valeur_vehicule = 0
        if np.random.random() < prob_vehicule:
            valeur_vehicule = revenu_mensuel * np.random.uniform(6, 24)

        # Épargne
        taux_epargne = max(0, np.random.normal(0.15, 0.1))
        epargne = revenu_mensuel * 12 * taux_epargne * patrimoine_multiplier

        # Autres actifs
        autres_actifs = 0
        if np.random.random() < 0.2:
            autres_actifs = revenu_mensuel * np.random.uniform(2, 12)

        patrimoine_total = valeur_immobilier + valeur_vehicule + epargne + autres_actifs

        # Dettes existantes
        # Dette immobilière (si propriétaire)
        dette_immobiliere = 0
        if valeur_immobilier > 0 and np.random.random() < 0.6:
            dette_immobiliere = valeur_immobilier * np.random.uniform(0.3, 0.8)

        # Dette auto
        dette_auto = 0
        if valeur_vehicule > 0 and np.random.random() < 0.4:
            dette_auto = valeur_vehicule * np.random.uniform(0.2, 0.7)

        # Dette personnelle
        prob_dette_perso = 0.3 - (revenu_mensuel / 5000) * 0.2
        dette_personnelle = 0
        if np.random.random() < prob_dette_perso:
            dette_personnelle = revenu_mensuel * np.random.uniform(2, 12)

        # Dette carte de crédit
        prob_carte = 0.4 + (revenu_mensuel / 3000) * 0.3
        dette_carte_credit = 0
        if np.random.random() < prob_carte:
            dette_carte_credit = revenu_mensuel * np.random.uniform(0.5, 3)

        dette_totale = dette_immobiliere + dette_auto + dette_personnelle + dette_carte_credit

        # Ratios financiers
        ratio_endettement = min(2.0, dette_totale / (revenu_total * 12)) if revenu_total > 0 else 0  # Plafonner à 200%
        charges_mensuelles = dette_totale * 0.08 + profile['nombre_enfants'] * 200  # Estimation charges
        reste_a_vivre = revenu_total - charges_mensuelles
        capacite_remboursement = max(0, reste_a_vivre * 0.3)  # 30% du reste à vivre

        # Garanties disponibles
        garanties_disponibles = patrimoine_total * 0.7  # 70% du patrimoine comme garantie

        return {
            'revenu_mensuel': round(revenu_mensuel, 2),
            'autres_revenus': round(autres_revenus, 2),
            'revenu_total': round(revenu_total, 2),
            'valeur_immobilier': round(valeur_immobilier, 2),
            'valeur_vehicule': round(valeur_vehicule, 2),
            'epargne': round(epargne, 2),
            'patrimoine_total': round(patrimoine_total, 2),
            'dette_immobiliere': round(dette_immobiliere, 2),
            'dette_auto': round(dette_auto, 2),
            'dette_personnelle': round(dette_personnelle, 2),
            'dette_totale': round(dette_totale, 2),
            'ratio_endettement': round(ratio_endettement, 3),
            'reste_a_vivre': round(reste_a_vivre, 2),
            'capacite_remboursement': round(capacite_remboursement, 2),
            'garanties_disponibles': round(garanties_disponibles, 2)
        }

    def generate_credit_history(self, profile: Dict, financial: Dict) -> Dict:
        """Génère l'historique de crédit du client"""
        # Ancienneté relation bancaire corrélée à l'âge
        anciennete_relation = min(profile['anciennete_emploi'], (profile['age'] - 18) * 12)
        anciennete_relation = max(6, int(np.random.exponential(scale=anciennete_relation * 0.7)))

        # Banque principale
        banque_principale = np.random.choice(BANQUES_TUNISIENNES)

        # Nombre de crédits antérieurs (corrélé à l'âge et au revenu)
        prob_credit = min(0.8, (profile['age'] - 25) * 0.02 + financial['revenu_mensuel'] / 5000)
        nombre_credits_anterieurs = np.random.poisson(prob_credit * 3)

        # Comportement de paiement (meilleur pour revenus élevés et éducation)
        base_behavior_score = 0.7
        if profile['niveau_education'] in ['universitaire', 'post_universitaire']:
            base_behavior_score += 0.15
        if financial['revenu_mensuel'] > 2000:
            base_behavior_score += 0.1
        if profile['secteur_activite'] == 'fonction_publique':
            base_behavior_score += 0.1

        regularite_paiements = min(1.0, np.random.beta(8, 2) * base_behavior_score + 0.3)

        # Retards basés sur la régularité
        if regularite_paiements > 0.9:
            retard_maximum_jours = np.random.choice([0, 5, 15], p=[0.7, 0.2, 0.1])
            nombre_incidents_12m = np.random.poisson(0.2)
        elif regularite_paiements > 0.7:
            retard_maximum_jours = np.random.choice([0, 15, 30, 60], p=[0.3, 0.4, 0.2, 0.1])
            nombre_incidents_12m = np.random.poisson(1.0)
        else:
            retard_maximum_jours = np.random.choice([30, 60, 90, 180], p=[0.3, 0.3, 0.3, 0.1])
            nombre_incidents_12m = np.random.poisson(2.5)

        # Utilisation du crédit
        taux_utilisation_credit = np.random.beta(2, 3) * 0.8  # Généralement < 80%

        # Demandes récentes (plus fréquentes pour les profils risqués)
        if regularite_paiements > 0.8:
            nombre_demandes_6m = np.random.poisson(0.5)
            nombre_rejets_12m = 0
        else:
            nombre_demandes_6m = np.random.poisson(1.5)
            nombre_rejets_12m = np.random.poisson(0.8)

        # Score comportement global
        score_comportement = (regularite_paiements * 0.4 +
                            (1 - min(1, nombre_incidents_12m / 5)) * 0.3 +
                            (1 - min(1, retard_maximum_jours / 180)) * 0.3)

        return {
            'nombre_credits_anterieurs': nombre_credits_anterieurs,
            'anciennete_relation_bancaire': anciennete_relation,
            'banque_principale': banque_principale,
            'retard_maximum_jours': retard_maximum_jours,
            'nombre_incidents_12m': nombre_incidents_12m,
            'nombre_demandes_6m': nombre_demandes_6m,
            'taux_utilisation_credit': round(taux_utilisation_credit, 3),
            'regularite_paiements': round(regularite_paiements, 3),
            'nombre_rejets_12m': nombre_rejets_12m,
            'score_comportement': round(score_comportement, 3)
        }

    def generate_credit_request(self, profile: Dict, financial: Dict) -> Dict:
        """Génère une demande de crédit"""
        # Type de crédit selon le profil
        if profile['age'] < 35 and profile['type_logement'] != 'proprietaire':
            type_probs = [0.4, 0.4, 0.15, 0.05]  # Plus de personnel et auto
        elif profile['age'] > 45:
            type_probs = [0.3, 0.2, 0.3, 0.2]  # Plus d'immobilier et professionnel
        else:
            type_probs = [0.35, 0.25, 0.25, 0.15]

        types_credit = ['personnel', 'immobilier', 'auto', 'professionnel']
        type_credit = np.random.choice(types_credit, p=type_probs)

        # Montant selon le type et le revenu
        if type_credit == 'personnel':
            montant_demande = financial['revenu_mensuel'] * np.random.uniform(3, 15)
            duree_demande = np.random.choice([12, 24, 36, 48, 60], p=[0.1, 0.2, 0.3, 0.3, 0.1])
        elif type_credit == 'auto':
            montant_demande = financial['revenu_mensuel'] * np.random.uniform(8, 30)
            duree_demande = np.random.choice([36, 48, 60, 72, 84], p=[0.1, 0.2, 0.4, 0.2, 0.1])
        elif type_credit == 'immobilier':
            montant_demande = financial['revenu_mensuel'] * np.random.uniform(30, 100)
            duree_demande = np.random.choice([120, 180, 240, 300], p=[0.2, 0.3, 0.3, 0.2])
        else:  # professionnel
            montant_demande = financial['revenu_mensuel'] * np.random.uniform(5, 25)
            duree_demande = np.random.choice([24, 36, 48, 60], p=[0.2, 0.3, 0.3, 0.2])

        # Taux proposé selon le type et le profil de risque
        taux_base = TAUX_CREDIT_TUNISIE[type_credit]['moyen']

        # Apport personnel
        if type_credit in ['immobilier', 'auto']:
            apport_personnel = montant_demande * np.random.uniform(0.1, 0.3)
        else:
            apport_personnel = 0

        # Calculs
        taux_mensuel = taux_base / 12
        mensualite_demandee = (montant_demande * taux_mensuel * (1 + taux_mensuel)**duree_demande) / ((1 + taux_mensuel)**duree_demande - 1)
        ratio_mensualite_revenu = mensualite_demandee / financial['revenu_mensuel']

        return {
            'montant_demande': round(montant_demande, 2),
            'duree_demande': duree_demande,
            'type_credit': type_credit,
            'mensualite_demandee': round(mensualite_demandee, 2),
            'taux_propose': round(taux_base, 4),
            'ratio_mensualite_revenu': round(ratio_mensualite_revenu, 3),
            'apport_personnel': round(apport_personnel, 2),
            'valeur_garanties': round(financial['garanties_disponibles'], 2)
        }

    def generate_sample_data(self, n_samples: int = 10) -> pd.DataFrame:
        """Génère un échantillon de données pour validation"""
        data = []

        for _ in range(n_samples):
            profile = self.generate_client_profile()
            financial = self.generate_financial_data(profile)
            credit_history = self.generate_credit_history(profile, financial)
            credit_request = self.generate_credit_request(profile, financial)

            # Combiner les données
            client_data = {**profile, **financial, **credit_history, **credit_request}
            data.append(client_data)

        return pd.DataFrame(data)

    def generate_risk_classification(self, profile: Dict, financial: Dict,
                                   credit_history: Dict, credit_request: Dict) -> Dict:
        """Génère la classification de risque et les scores PD/LGD/EAD"""

        # Calcul du score de risque basé sur plusieurs facteurs
        risk_score = 0.0

        # Facteurs démographiques (20%)
        if profile['age'] < 25 or profile['age'] > 60:
            risk_score += 0.05
        if profile['secteur_activite'] in ['agriculture', 'construction', 'tourisme']:
            risk_score += 0.08
        elif profile['secteur_activite'] in ['fonction_publique', 'sante', 'education']:
            risk_score -= 0.05
        if profile['niveau_education'] in ['universitaire', 'post_universitaire']:
            risk_score -= 0.03

        # Facteurs financiers (40%)
        if financial['ratio_endettement'] > 0.4:
            risk_score += 0.15
        elif financial['ratio_endettement'] < 0.2:
            risk_score -= 0.05

        if credit_request['ratio_mensualite_revenu'] > 0.35:
            risk_score += 0.12
        elif credit_request['ratio_mensualite_revenu'] < 0.2:
            risk_score -= 0.03

        if financial['patrimoine_total'] > financial['revenu_total'] * 5:
            risk_score -= 0.08

        # Facteurs comportementaux (40%)
        risk_score += (1 - credit_history['score_comportement']) * 0.25

        if credit_history['nombre_incidents_12m'] > 2:
            risk_score += 0.1
        if credit_history['retard_maximum_jours'] > 90:
            risk_score += 0.15

        # Normaliser le score entre 0 et 1
        risk_score = max(0, min(1, risk_score + np.random.normal(0, 0.05)))

        # Classification BCT selon le score de risque
        if risk_score < 0.05:
            classe_risque = 'C0'
        elif risk_score < 0.15:
            classe_risque = 'C1'
        elif risk_score < 0.3:
            classe_risque = 'C2'
        elif risk_score < 0.5:
            classe_risque = 'C3'
        elif risk_score < 0.7:
            classe_risque = 'C4'
        else:
            classe_risque = 'C5'

        # SED flag (Situation En Difficulté)
        situation_contentieux = risk_score > 0.6 and np.random.random() < 0.3

        # Default flag (variable cible principale)
        default_flag = risk_score > 0.4 and np.random.random() < (risk_score * 0.8)

        # Scores PD, LGD, EAD
        score_pd = max(0.001, min(0.99, risk_score * 0.8 + np.random.normal(0, 0.05)))  # Probabilité de défaut
        score_lgd = np.random.beta(2, 5) * 0.6 + 0.1  # Loss Given Default (10-70%)
        score_ead = credit_request['montant_demande'] * np.random.uniform(0.8, 1.0)  # Exposure at Default

        # Perte attendue = PD × LGD × EAD
        perte_attendue = score_pd * score_lgd * score_ead

        # Décision finale
        if risk_score < 0.2 and credit_request['ratio_mensualite_revenu'] < 0.3:
            decision_finale = 'APPROVE'
        elif risk_score > 0.5 or credit_request['ratio_mensualite_revenu'] > 0.4:
            decision_finale = 'REJECT'
        else:
            decision_finale = 'MANUAL_REVIEW'

        return {
            'classe_risque': classe_risque,
            'situation_contentieux': situation_contentieux,
            'default_flag': default_flag,
            'score_pd': round(score_pd, 4),
            'score_lgd': round(score_lgd, 4),
            'score_ead': round(score_ead, 2),
            'perte_attendue': round(perte_attendue, 2),
            'decision_finale': decision_finale
        }

    def generate_full_dataset(self, n_clients: int = None) -> pd.DataFrame:
        """Génère le dataset complet avec toutes les variables"""
        if n_clients is None:
            n_clients = self.n_clients

        print(f"Génération de {n_clients} clients tunisiens...")
        data = []

        for i in range(n_clients):
            if (i + 1) % 10000 == 0:
                print(f"Progression: {i + 1}/{n_clients} clients générés")

            # Générer toutes les composantes
            profile = self.generate_client_profile()
            financial = self.generate_financial_data(profile)
            credit_history = self.generate_credit_history(profile, financial)
            credit_request = self.generate_credit_request(profile, financial)
            risk_classification = self.generate_risk_classification(
                profile, financial, credit_history, credit_request
            )

            # Combiner toutes les données
            client_data = {
                **profile, **financial, **credit_history,
                **credit_request, **risk_classification
            }
            data.append(client_data)

        print(f"Dataset généré avec succès: {len(data)} clients")
        return pd.DataFrame(data)

if __name__ == "__main__":
    # Test avec 10 échantillons
    generator = TunisianCreditDataGenerator()
    sample_df = generator.generate_sample_data(10)

    print("=== ÉCHANTILLON DE 10 CLIENTS TUNISIENS ===")
    print(sample_df[['client_id', 'age', 'profession', 'region', 'revenu_mensuel', 'secteur_activite']].to_string())

    print(f"\n=== STATISTIQUES ===")
    print(f"Âge moyen: {sample_df['age'].mean():.1f} ans")
    print(f"Revenu moyen: {sample_df['revenu_mensuel'].mean():.0f} TND")
    print(f"Répartition par région:")
    print(sample_df['region'].value_counts())

    # Test du dataset complet avec 1000 clients
    print(f"\n=== TEST DATASET COMPLET (1000 clients) ===")
    full_df = generator.generate_full_dataset(1000)
    print(f"Colonnes générées: {len(full_df.columns)}")
    print(f"Répartition des décisions:")
    print(full_df['decision_finale'].value_counts())
    print(f"Répartition des classes de risque:")
    print(full_df['classe_risque'].value_counts())
