"""
Schéma complet des données clients pour le scoring de crédit tunisien
Conforme aux exigences BCT et CIBCT
"""

from dataclasses import dataclass
from typing import Optional, List
from datetime import datetime

@dataclass
class ClientProfile:
    """Profil démographique du client"""
    # Identifiants
    client_id: str
    cin: str  # Carte d'identité nationale

    # Démographie
    age: int
    sexe: str  # 'M' ou 'F'
    categorie_client: str  # 'tunisien_resident', 'tunisien_resident_etranger', 'etranger_resident_tunisie'
    nationalite: str  # 'tunisienne' ou autre pour ERT
    pays_residence: str  # 'tunisie' ou pays étranger pour TRE
    situation_familiale: str
    nombre_enfants: int
    niveau_education: str
    region: str

    # Professionnel
    profession: str
    secteur_activite: str
    anciennete_emploi: int  # en mois
    type_contrat: str  # 'CDI', 'CDD', 'freelance', 'fonctionnaire'

    # Logement
    type_logement: str  # 'proprietaire', 'locataire', 'famille'
    anciennete_logement: int  # en mois

@dataclass
class FinancialData:
    """Données financières du client"""
    # Revenus
    revenu_mensuel: float  # TND
    autres_revenus: float  # TND (loyers, investissements, etc.)
    revenu_total: float  # TND

    # Patrimoine
    valeur_immobilier: float  # TND
    valeur_vehicule: float  # TND
    epargne: float  # TND
    autres_actifs: float  # TND
    patrimoine_total: float  # TND

    # Dettes existantes
    dette_immobiliere: float  # TND
    dette_auto: float  # TND
    dette_personnelle: float  # TND
    dette_carte_credit: float  # TND
    dette_totale: float  # TND

    # Ratios financiers
    ratio_endettement: float  # dette_totale / revenu_total
    reste_a_vivre: float  # revenu - charges
    capacite_remboursement: float  # % du revenu disponible

@dataclass
class CreditRequest:
    """Demande de crédit actuelle"""
    montant_demande: float  # TND
    duree_demande: int  # en mois
    type_credit: str  # 'personnel', 'immobilier', 'auto', 'professionnel'
    objet_credit: str
    garanties: float  # valeur des garanties proposées
    apport_personnel: float  # TND

    # Calculs
    mensualite_demandee: float  # TND
    taux_propose: float
    ratio_mensualite_revenu: float

@dataclass
class CreditHistory:
    """Historique de crédit du client"""
    # Historique général
    nombre_credits_anterieurs: int
    nombre_credits_actuels: int
    anciennete_relation_bancaire: int  # en mois
    banque_principale: str

    # Comportement de paiement
    retard_maximum_jours: int
    nombre_incidents_12m: int  # incidents dans les 12 derniers mois
    nombre_incidents_total: int

    # Activité récente
    nombre_demandes_6m: int  # demandes de crédit dans les 6 derniers mois
    nombre_rejets_12m: int

    # Utilisation du crédit
    taux_utilisation_credit: float  # % du crédit utilisé vs disponible
    regularite_paiements: float  # score de régularité (0-1)

@dataclass
class RiskClassification:
    """Classification de risque selon BCT"""
    classe_risque: str  # C0, C1, C2, C3, C4, C5
    situation_contentieux: bool  # SED flag
    date_derniere_classification: datetime

    # Scores calculés
    score_pd: float  # Probabilité de défaut (0-1)
    score_lgd: float  # Loss Given Default (0-1)
    score_ead: float  # Exposure at Default
    perte_attendue: float  # Expected Loss = PD × LGD × EAD

    # Décision
    decision_automatique: str  # 'APPROVE', 'REJECT', 'MANUAL_REVIEW'
    limite_credit_recommandee: float  # TND

@dataclass
class ComplianceData:
    """Données de conformité BCT/CIBCT"""
    # Vérifications KYC
    kyc_complete: bool
    documents_valides: bool
    verification_revenus: bool

    # Listes de surveillance
    liste_noire: bool
    sanctions_internationales: bool
    pep_status: bool  # Politically Exposed Person

    # Scoring externe
    score_cibct: Optional[float]  # Score CIBCT si disponible
    derniere_consultation_cibct: Optional[datetime]

# Schéma complet du dataset
CLIENT_DATASET_SCHEMA = {
    # Profil client (15 colonnes)
    'client_id': 'string',
    'cin': 'string',
    'age': 'int',
    'sexe': 'string',
    'situation_familiale': 'string',
    'nombre_enfants': 'int',
    'niveau_education': 'string',
    'region': 'string',
    'profession': 'string',
    'secteur_activite': 'string',
    'anciennete_emploi': 'int',
    'type_contrat': 'string',
    'type_logement': 'string',
    'anciennete_logement': 'int',
    'taux_chomage_sectoriel': 'float',

    # Données financières (15 colonnes)
    'revenu_mensuel': 'float',
    'autres_revenus': 'float',
    'revenu_total': 'float',
    'valeur_immobilier': 'float',
    'valeur_vehicule': 'float',
    'epargne': 'float',
    'patrimoine_total': 'float',
    'dette_immobiliere': 'float',
    'dette_auto': 'float',
    'dette_personnelle': 'float',
    'dette_totale': 'float',
    'ratio_endettement': 'float',
    'reste_a_vivre': 'float',
    'capacite_remboursement': 'float',
    'garanties_disponibles': 'float',

    # Demande de crédit (8 colonnes)
    'montant_demande': 'float',
    'duree_demande': 'int',
    'type_credit': 'string',
    'mensualite_demandee': 'float',
    'taux_propose': 'float',
    'ratio_mensualite_revenu': 'float',
    'apport_personnel': 'float',
    'valeur_garanties': 'float',

    # Historique de crédit (10 colonnes)
    'nombre_credits_anterieurs': 'int',
    'anciennete_relation_bancaire': 'int',
    'banque_principale': 'string',
    'retard_maximum_jours': 'int',
    'nombre_incidents_12m': 'int',
    'nombre_demandes_6m': 'int',
    'taux_utilisation_credit': 'float',
    'regularite_paiements': 'float',
    'nombre_rejets_12m': 'int',
    'score_comportement': 'float',

    # Variables cibles et classifications (8 colonnes)
    'classe_risque': 'string',  # C0-C5
    'situation_contentieux': 'bool',  # SED
    'default_flag': 'bool',  # Variable cible principale
    'score_pd': 'float',  # Probabilité de défaut
    'score_lgd': 'float',  # Loss Given Default
    'score_ead': 'float',  # Exposure at Default
    'perte_attendue': 'float',  # Expected Loss
    'decision_finale': 'string'  # APPROVE/REJECT/MANUAL_REVIEW
}

# Total: 56 colonnes
print(f"Nombre total de colonnes dans le dataset: {len(CLIENT_DATASET_SCHEMA)}")
