"""
Script de validation du dataset synthétique tunisien
Vérifie la qualité, cohérence et réalisme des données générées
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

def load_dataset(filename="output/tunisian_credit_sample_1k.csv"):
    """Charge le dataset pour validation"""
    if not Path(filename).exists():
        print(f"❌ Fichier non trouvé: {filename}")
        return None
    
    df = pd.read_csv(filename)
    print(f"✅ Dataset chargé: {len(df)} lignes, {len(df.columns)} colonnes")
    return df

def validate_data_quality(df):
    """Valide la qualité des données"""
    print("\n🔍 VALIDATION QUALITÉ DES DONNÉES")
    print("-" * 50)
    
    # Valeurs manquantes
    missing = df.isnull().sum()
    if missing.sum() == 0:
        print("✅ Aucune valeur manquante")
    else:
        print(f"❌ {missing.sum()} valeurs manquantes trouvées")
        print(missing[missing > 0])
    
    # Doublons
    duplicates = df.duplicated().sum()
    if duplicates == 0:
        print("✅ Aucun doublon")
    else:
        print(f"❌ {duplicates} doublons trouvés")
    
    # Types de données
    print(f"\n📊 Types de données:")
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    string_cols = df.select_dtypes(include=['object']).columns
    bool_cols = df.select_dtypes(include=['bool']).columns
    
    print(f"  Numériques: {len(numeric_cols)}")
    print(f"  Texte: {len(string_cols)}")
    print(f"  Booléens: {len(bool_cols)}")

def validate_business_rules(df):
    """Valide les règles métier"""
    print("\n📋 VALIDATION RÈGLES MÉTIER")
    print("-" * 50)
    
    errors = []
    
    # Âge cohérent
    invalid_age = ((df['age'] < 18) | (df['age'] > 80)).sum()
    if invalid_age > 0:
        errors.append(f"❌ {invalid_age} âges invalides (< 18 ou > 80)")
    else:
        print("✅ Âges cohérents")
    
    # Revenus positifs
    negative_revenue = (df['revenu_mensuel'] <= 0).sum()
    if negative_revenue > 0:
        errors.append(f"❌ {negative_revenue} revenus négatifs ou nuls")
    else:
        print("✅ Revenus positifs")
    
    # Cohérence patrimoine/logement
    owner_no_property = ((df['type_logement'] == 'proprietaire') & 
                        (df['valeur_immobilier'] == 0)).sum()
    if owner_no_property > 0:
        errors.append(f"❌ {owner_no_property} propriétaires sans immobilier")
    else:
        print("✅ Cohérence logement/patrimoine")
    
    # Cohérence enfants/situation familiale
    single_with_kids = ((df['situation_familiale'] == 'celibataire') & 
                       (df['nombre_enfants'] > 0)).sum()
    if single_with_kids > 0:
        errors.append(f"⚠️ {single_with_kids} célibataires avec enfants")
    
    # Ratios financiers cohérents
    extreme_ratios = (df['ratio_endettement'] > 5).sum()  # > 500%
    if extreme_ratios > 0:
        errors.append(f"⚠️ {extreme_ratios} ratios d'endettement extrêmes (>500%)")
    
    # Scores PD dans la plage [0,1]
    invalid_pd = ((df['score_pd'] < 0) | (df['score_pd'] > 1)).sum()
    if invalid_pd > 0:
        errors.append(f"❌ {invalid_pd} scores PD hors plage [0,1]")
    else:
        print("✅ Scores PD valides")
    
    # Classes de risque cohérentes avec scores PD
    c0_high_pd = ((df['classe_risque'] == 'C0') & (df['score_pd'] > 0.2)).sum()
    c5_low_pd = ((df['classe_risque'] == 'C5') & (df['score_pd'] < 0.5)).sum()
    
    if c0_high_pd > 0:
        errors.append(f"⚠️ {c0_high_pd} clients C0 avec PD élevé")
    if c5_low_pd > 0:
        errors.append(f"⚠️ {c5_low_pd} clients C5 avec PD faible")
    
    if not errors:
        print("✅ Toutes les règles métier respectées")
    else:
        for error in errors:
            print(error)

def validate_distributions(df):
    """Valide les distributions statistiques"""
    print("\n📈 VALIDATION DISTRIBUTIONS")
    print("-" * 50)
    
    # Distribution des âges
    age_mean = df['age'].mean()
    age_std = df['age'].std()
    print(f"Âge: μ={age_mean:.1f}, σ={age_std:.1f}")
    
    if 35 <= age_mean <= 45:
        print("✅ Âge moyen réaliste")
    else:
        print(f"⚠️ Âge moyen inhabituel: {age_mean:.1f}")
    
    # Distribution des revenus
    revenue_median = df['revenu_mensuel'].median()
    revenue_mean = df['revenu_mensuel'].mean()
    print(f"Revenu: médiane={revenue_median:.0f} TND, moyenne={revenue_mean:.0f} TND")
    
    if 1000 <= revenue_median <= 2500:
        print("✅ Revenu médian réaliste pour la Tunisie")
    else:
        print(f"⚠️ Revenu médian inhabituel: {revenue_median:.0f} TND")
    
    # Distribution des régions
    tunis_pct = (df['region'] == 'tunis').mean() * 100
    print(f"Population Tunis: {tunis_pct:.1f}%")
    
    if 20 <= tunis_pct <= 30:
        print("✅ Répartition régionale réaliste")
    else:
        print(f"⚠️ Surreprésentation/sous-représentation de Tunis")
    
    # Distribution des décisions
    approve_rate = (df['decision_finale'] == 'APPROVE').mean() * 100
    reject_rate = (df['decision_finale'] == 'REJECT').mean() * 100
    
    print(f"Taux d'approbation: {approve_rate:.1f}%")
    print(f"Taux de rejet: {reject_rate:.1f}%")
    
    if 20 <= approve_rate <= 40:
        print("✅ Taux d'approbation réaliste")
    else:
        print(f"⚠️ Taux d'approbation inhabituel: {approve_rate:.1f}%")

def validate_correlations(df):
    """Valide les corrélations attendues"""
    print("\n🔗 VALIDATION CORRÉLATIONS")
    print("-" * 50)
    
    # Corrélation âge-revenu
    age_revenue_corr = df['age'].corr(df['revenu_mensuel'])
    print(f"Corrélation âge-revenu: {age_revenue_corr:.3f}")
    
    if age_revenue_corr > 0.1:
        print("✅ Corrélation positive âge-revenu")
    else:
        print("⚠️ Corrélation âge-revenu faible")
    
    # Corrélation éducation-revenu
    education_mapping = {
        'primaire': 1, 'secondaire': 2, 'technique': 3,
        'universitaire': 4, 'post_universitaire': 5
    }
    df_temp = df.copy()
    df_temp['education_num'] = df_temp['niveau_education'].map(education_mapping)
    edu_revenue_corr = df_temp['education_num'].corr(df_temp['revenu_mensuel'])
    print(f"Corrélation éducation-revenu: {edu_revenue_corr:.3f}")
    
    if edu_revenue_corr > 0.2:
        print("✅ Corrélation positive éducation-revenu")
    else:
        print("⚠️ Corrélation éducation-revenu faible")
    
    # Corrélation score PD - classe de risque
    risk_mapping = {'C0': 0, 'C1': 1, 'C2': 2, 'C3': 3, 'C4': 4, 'C5': 5}
    df_temp['risk_num'] = df_temp['classe_risque'].map(risk_mapping)
    pd_risk_corr = df_temp['score_pd'].corr(df_temp['risk_num'])
    print(f"Corrélation PD-classe risque: {pd_risk_corr:.3f}")
    
    if pd_risk_corr > 0.5:
        print("✅ Forte corrélation PD-classe de risque")
    else:
        print("⚠️ Corrélation PD-classe de risque faible")

def generate_summary_report(df):
    """Génère un rapport de synthèse"""
    print("\n📄 RAPPORT DE SYNTHÈSE")
    print("=" * 50)
    
    print(f"Dataset: {len(df)} clients, {len(df.columns)} variables")
    print(f"Période: Données synthétiques tunisiennes 2024")
    
    print(f"\n🎯 INDICATEURS CLÉS:")
    print(f"  • Âge moyen: {df['age'].mean():.1f} ans")
    print(f"  • Revenu médian: {df['revenu_mensuel'].median():.0f} TND")
    print(f"  • Taux d'approbation: {(df['decision_finale'] == 'APPROVE').mean()*100:.1f}%")
    print(f"  • Score PD moyen: {df['score_pd'].mean():.3f}")
    print(f"  • Ratio endettement moyen: {df['ratio_endettement'].mean():.1%}")
    
    print(f"\n🏆 QUALITÉ DES DONNÉES:")
    quality_score = 100
    
    # Déductions pour problèmes
    if df.isnull().sum().sum() > 0:
        quality_score -= 20
    if df.duplicated().sum() > 0:
        quality_score -= 15
    if ((df['age'] < 18) | (df['age'] > 80)).sum() > 0:
        quality_score -= 10
    if (df['revenu_mensuel'] <= 0).sum() > 0:
        quality_score -= 25
    
    print(f"  Score qualité: {quality_score}/100")
    
    if quality_score >= 90:
        print("  🟢 Excellente qualité")
    elif quality_score >= 75:
        print("  🟡 Bonne qualité")
    else:
        print("  🔴 Qualité à améliorer")

def main():
    """Fonction principale de validation"""
    print("🔍 VALIDATION DATASET SYNTHÉTIQUE TUNISIEN")
    print("=" * 60)
    
    # Charger le dataset
    df = load_dataset()
    if df is None:
        return
    
    # Exécuter toutes les validations
    validate_data_quality(df)
    validate_business_rules(df)
    validate_distributions(df)
    validate_correlations(df)
    generate_summary_report(df)
    
    print(f"\n✅ VALIDATION TERMINÉE")

if __name__ == "__main__":
    main()
