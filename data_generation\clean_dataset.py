"""
Script de nettoyage du dataset multicatégories
Simule les étapes de nettoyage qu'on ferait sur des données réelles
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def clean_dataset():
    """Nettoie le dataset multicatégories"""

    print("=" * 60)
    print("NETTOYAGE DATASET MULTICATÉGORIES")
    print("=" * 60)

    # Charger le dataset
    input_file = "output/tunisian_credit_multicategories_100k.csv"
    df = pd.read_csv(input_file)

    print(f"📊 Dataset original: {len(df):,} clients, {len(df.columns)} colonnes")

    # 1. VÉRIFICATION DES VALEURS MANQUANTES
    print(f"\n🔍 VÉRIFICATION VALEURS MANQUANTES")
    print("-" * 40)

    missing_counts = df.isnull().sum()
    missing_cols = missing_counts[missing_counts > 0]

    if len(missing_cols) > 0:
        print("Colonnes avec valeurs manquantes:")
        for col, count in missing_cols.items():
            pct = count / len(df) * 100
            print(f"  {col}: {count:,} ({pct:.1f}%)")
    else:
        print("✅ Aucune valeur manquante détectée")

    # 2. NETTOYAGE DES DOUBLONS
    print(f"\n🔄 NETTOYAGE DES DOUBLONS")
    print("-" * 40)

    # Vérifier doublons sur CIN
    doublons_cin = df.duplicated(subset=['cin']).sum()
    if doublons_cin > 0:
        print(f"⚠️ {doublons_cin} doublons CIN détectés - suppression...")
        df = df.drop_duplicates(subset=['cin'], keep='first')
    else:
        print("✅ Aucun doublon CIN")

    # Vérifier doublons complets
    doublons_complets = df.duplicated().sum()
    if doublons_complets > 0:
        print(f"⚠️ {doublons_complets} doublons complets détectés - suppression...")
        df = df.drop_duplicates(keep='first')
    else:
        print("✅ Aucun doublon complet")

    # 3. VALIDATION DES PLAGES DE VALEURS
    print(f"\n📏 VALIDATION DES PLAGES")
    print("-" * 40)

    # Âges aberrants
    ages_aberrants = ((df['age'] < 18) | (df['age'] > 80)).sum()
    if ages_aberrants > 0:
        print(f"⚠️ {ages_aberrants} âges aberrants détectés - correction...")
        df.loc[df['age'] < 18, 'age'] = 18
        df.loc[df['age'] > 80, 'age'] = 80
    else:
        print("✅ Âges dans les plages normales")

    # Revenus négatifs
    revenus_negatifs = (df['revenu_mensuel'] < 0).sum()
    if revenus_negatifs > 0:
        print(f"⚠️ {revenus_negatifs} revenus négatifs détectés - correction...")
        df.loc[df['revenu_mensuel'] < 0, 'revenu_mensuel'] = 500  # Salaire minimum
    else:
        print("✅ Revenus positifs")

    # Scores PD hors plage [0,1]
    pd_hors_plage = ((df['score_pd'] < 0) | (df['score_pd'] > 1)).sum()
    if pd_hors_plage > 0:
        print(f"⚠️ {pd_hors_plage} scores PD hors plage détectés - correction...")
        df.loc[df['score_pd'] < 0, 'score_pd'] = 0.001
        df.loc[df['score_pd'] > 1, 'score_pd'] = 0.999
    else:
        print("✅ Scores PD dans [0,1]")

    # 4. STANDARDISATION DES FORMATS
    print(f"\n🔧 STANDARDISATION DES FORMATS")
    print("-" * 40)

    # Sexe en majuscules
    df['sexe'] = df['sexe'].str.upper()
    print("✅ Sexe standardisé (M/F)")

    # Nationalités en minuscules
    df['nationalite'] = df['nationalite'].str.lower()
    print("✅ Nationalités en minuscules")

    # Pays de résidence en minuscules
    df['pays_residence'] = df['pays_residence'].str.lower()
    print("✅ Pays de résidence en minuscules")

    # Classes de risque en majuscules
    df['classe_risque'] = df['classe_risque'].str.upper()
    print("✅ Classes de risque standardisées")

    # 5. VALIDATION COHÉRENCE MÉTIER
    print(f"\n🏦 VALIDATION COHÉRENCE MÉTIER")
    print("-" * 40)

    # Cohérence nationalité/résidence
    incoherences = 0

    # TRE doivent avoir nationalité tunisienne et résidence != tunisie
    tre_incoherent = df[
        (df['categorie_client'] == 'tunisien_resident_etranger') &
        ((df['nationalite'] != 'tunisienne') | (df['pays_residence'] == 'tunisie'))
    ]
    if len(tre_incoherent) > 0:
        print(f"⚠️ {len(tre_incoherent)} TRE incohérents détectés - correction...")
        df.loc[tre_incoherent.index, 'nationalite'] = 'tunisienne'
        df.loc[tre_incoherent.index, 'pays_residence'] = 'france'  # Par défaut
        incoherences += len(tre_incoherent)

    # ERT doivent avoir nationalité != tunisienne et résidence = tunisie
    ert_incoherent = df[
        (df['categorie_client'] == 'etranger_resident_tunisie') &
        ((df['nationalite'] == 'tunisienne') | (df['pays_residence'] != 'tunisie'))
    ]
    if len(ert_incoherent) > 0:
        print(f"⚠️ {len(ert_incoherent)} ERT incohérents détectés - correction...")
        df.loc[ert_incoherent.index, 'nationalite'] = 'francaise'  # Par défaut
        df.loc[ert_incoherent.index, 'pays_residence'] = 'tunisie'
        incoherences += len(ert_incoherent)

    if incoherences == 0:
        print("✅ Cohérence nationalité/résidence validée")

    # 6. CRÉATION D'INDICATEURS DE QUALITÉ
    print(f"\n📊 CRÉATION INDICATEURS QUALITÉ")
    print("-" * 40)

    # Score de qualité des données (0-100)
    df['data_quality_score'] = 100

    # Pénalités pour données manquantes (simulées)
    df.loc[df['revenu_mensuel'] == 500, 'data_quality_score'] -= 10  # Revenu minimum = suspect
    df.loc[df['age'].isin([18, 80]), 'data_quality_score'] -= 5     # Âges limites = suspect

    # Bonus pour données cohérentes
    df.loc[df['anciennete_emploi'] > df['age'] * 6, 'data_quality_score'] += 5  # Ancienneté cohérente

    print(f"✅ Score qualité ajouté (moyenne: {df['data_quality_score'].mean():.1f})")

    # 7. EXPORT DU DATASET NETTOYÉ
    print(f"\n💾 EXPORT DATASET NETTOYÉ")
    print("-" * 40)

    # Dataset principal nettoyé
    output_file = "output/tunisian_credit_multicategories_100k_clean.csv"
    df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"✅ Dataset nettoyé exporté: {output_file}")

    # Rapport de nettoyage
    rapport = {
        'date_nettoyage': datetime.now().isoformat(),
        'clients_avant': int(len(pd.read_csv(input_file))),
        'clients_apres': int(len(df)),
        'colonnes': int(len(df.columns)),
        'doublons_supprimes': int(doublons_cin + doublons_complets),
        'corrections_appliquees': {
            'ages_aberrants': int(ages_aberrants),
            'revenus_negatifs': int(revenus_negatifs),
            'scores_pd_corriges': int(pd_hors_plage),
            'incoherences_metier': int(incoherences)
        },
        'qualite_moyenne': float(df['data_quality_score'].mean()),
        'taille_finale_mb': float(df.memory_usage(deep=True).sum() / 1024**2)
    }

    import json
    rapport_file = "output/rapport_nettoyage.json"
    with open(rapport_file, 'w', encoding='utf-8') as f:
        json.dump(rapport, f, indent=2, ensure_ascii=False)
    print(f"✅ Rapport de nettoyage: {rapport_file}")

    # 8. STATISTIQUES FINALES
    print(f"\n📋 STATISTIQUES FINALES")
    print("-" * 40)
    print(f"Clients finaux: {len(df):,}")
    print(f"Colonnes: {len(df.columns)}")
    print(f"Qualité moyenne: {df['data_quality_score'].mean():.1f}/100")
    print(f"Taille: {df.memory_usage(deep=True).sum() / 1024**2:.1f} MB")

    print(f"\n🎉 NETTOYAGE TERMINÉ AVEC SUCCÈS!")

    return df

if __name__ == "__main__":
    clean_dataset()
