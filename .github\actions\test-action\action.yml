name: Test
description: Test the project

runs:
  using: composite
  steps:
    - name: Install Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 18.12.1

    - name: Install pnpm
      uses: pnpm/action-setup@v2
      id: pnpm-install
      with:
        version: 8
        run_install: false

    - name: Get pnpm store directory
      id: pnpm-cache
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

    - uses: actions/cache@v4
      name: Setup pnpm cache
      with:
        path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Install dependencies
      shell: bash
      run: pnpm install

    - name: Test
      shell: bash
      run: |
        export NODE_OPTIONS="--max-old-space-size=8192"
        pnpm test -- --verbose false
      env:
        ENVIRONMENT_NAME: test
        JEST_HTML_REPORTER_PAGE_TITLE: ${{ github.ref_name }}

    - name: Store test result
      uses: actions/upload-artifact@v4.3.3
      id: artifact-upload-step
      with:
        name: test-report.html
        path: services/workflows-service/ci/test-report.html
        retention-days: 7

