"""
Script de test pour les nouvelles catégories de clients
Tunisiens résidents, TRE, ERT
"""

from synthetic_data_generator import TunisianCreditDataGenerator
import pandas as pd

def test_categories():
    print("🧪 TEST DES NOUVELLES CATÉGORIES DE CLIENTS")
    print("=" * 60)
    
    # Générer un échantillon de test
    generator = TunisianCreditDataGenerator(n_clients=1000, random_seed=42)
    df = generator.generate_full_dataset()
    
    print(f"📊 Dataset généré: {len(df)} clients")
    
    # Analyser les catégories
    print(f"\n🏷️ RÉPARTITION DES CATÉGORIES:")
    categories = df['categorie_client'].value_counts()
    for cat, count in categories.items():
        pct = count / len(df) * 100
        print(f"  {cat:<30}: {count:>4} ({pct:>5.1f}%)")
    
    # Analyser les nationalités
    print(f"\n🌍 RÉPARTITION DES NATIONALITÉS:")
    nationalites = df['nationalite'].value_counts()
    for nat, count in nationalites.items():
        pct = count / len(df) * 100
        print(f"  {nat:<30}: {count:>4} ({pct:>5.1f}%)")
    
    # Analyser les pays de résidence
    print(f"\n🏠 RÉPARTITION DES PAYS DE RÉSIDENCE:")
    pays = df['pays_residence'].value_counts()
    for p, count in pays.items():
        pct = count / len(df) * 100
        print(f"  {p:<30}: {count:>4} ({pct:>5.1f}%)")
    
    # Analyser les revenus par catégorie
    print(f"\n💰 REVENUS MOYENS PAR CATÉGORIE:")
    for cat in df['categorie_client'].unique():
        cat_df = df[df['categorie_client'] == cat]
        revenu_moyen = cat_df['revenu_mensuel'].mean()
        revenu_median = cat_df['revenu_mensuel'].median()
        print(f"  {cat:<30}: {revenu_moyen:>8.0f} TND (moyenne), {revenu_median:>8.0f} TND (médiane)")
    
    # Analyser les TRE par pays
    print(f"\n🌍 TUNISIENS RÉSIDENTS À L'ÉTRANGER PAR PAYS:")
    tre_df = df[df['categorie_client'] == 'tunisien_resident_etranger']
    if len(tre_df) > 0:
        for pays in tre_df['pays_residence'].unique():
            pays_df = tre_df[tre_df['pays_residence'] == pays]
            count = len(pays_df)
            revenu_moyen = pays_df['revenu_mensuel'].mean()
            pct = count / len(tre_df) * 100
            print(f"  {pays:<20}: {count:>3} clients ({pct:>5.1f}%) - Revenu moyen: {revenu_moyen:>8.0f} TND")
    
    # Analyser les ERT par nationalité
    print(f"\n🏛️ ÉTRANGERS RÉSIDENTS EN TUNISIE PAR NATIONALITÉ:")
    ert_df = df[df['categorie_client'] == 'etranger_resident_tunisie']
    if len(ert_df) > 0:
        for nat in ert_df['nationalite'].unique():
            nat_df = ert_df[ert_df['nationalite'] == nat]
            count = len(nat_df)
            revenu_moyen = nat_df['revenu_mensuel'].mean()
            pct = count / len(ert_df) * 100
            print(f"  {nat:<20}: {count:>3} clients ({pct:>5.1f}%) - Revenu moyen: {revenu_moyen:>8.0f} TND")
    
    # Analyser les décisions par catégorie
    print(f"\n⚖️ DÉCISIONS DE CRÉDIT PAR CATÉGORIE:")
    for cat in df['categorie_client'].unique():
        cat_df = df[df['categorie_client'] == cat]
        decisions = cat_df['decision_finale'].value_counts()
        print(f"\n  {cat}:")
        for decision, count in decisions.items():
            pct = count / len(cat_df) * 100
            print(f"    {decision:<15}: {count:>3} ({pct:>5.1f}%)")
    
    # Export pour analyse détaillée
    output_file = "output/test_categories_1k.csv"
    df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"\n💾 Dataset exporté vers: {output_file}")
    
    print(f"\n✅ TEST TERMINÉ - Nouvelles catégories fonctionnelles!")

if __name__ == "__main__":
    test_categories()
