---
name: Bug report
about: Create a report to help us improve
title: Bug - subject of the issue
labels: bug
assignees: ''

---

### Description
Elaborate on the subject, motivation, and context.

### Has the bug been reported before
(Y/N) - Provide a link to the issue.

### Expected Behaviour
What should happen?

### Actual Behaviour
What happens instead?

### Environment
* Node version:
* Device, operating system and its version:
  *
* Browser and its version:
  *
* Version of the affected apps and packages:
  *

### Steps to Reproduce
1. Use a list format to describe which commands to run, and which actions to take.

### Any possible solutions
Describe the possible solutions you've considered, refer to files and lines of code when possible.

### If the bug is confirmed, would you be willing to submit a PR
(Y/N)

### Examples and references
Links, logs, screenshots, and other resources to help us understand the issue.
