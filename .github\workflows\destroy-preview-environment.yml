# Destroys a temporary environment that was created forwhen a pull request is created / updated with a 'deploy-pr' label or triggerred manually
name: Destroy PR Environment
concurrency:
  group: "deploy-${{ github.event.pull_request.head.ref }}"
  cancel-in-progress: false

on:
  workflow_dispatch:
  pull_request:
    types: [ closed, unlabeled ]

permissions:
  id-token: write
  contents: write

env:
  REF: ${{ github.event_name == 'workflow_dispatch' && github.ref || github.event_name == 'pull_request' && github.event.pull_request.head.ref }}

jobs:
  deploy-dev-pr-environment:
    if: |
      (github.event_name == 'pull_request' && github.event.action == 'unlabeled' && github.event.label.name == 'deploy-pr') 
      ||
      (github.event_name == 'pull_request' && github.event.action == 'closed' && contains(github.event.pull_request.labels.*.name, 'deploy-pr'))
      ||
      github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    outputs:
      env_name: ${{ steps.env-name.outputs.PR_ENV_NAME }}
    steps:
      - name: Clean Ref
        id: clean-ref
        shell: bash
        run: |
          BRANCH_NAME=${{ env.REF }}
          CLEAN_BRANCH_NAME=${BRANCH_NAME#refs/heads/}
          echo "ref=$CLEAN_BRANCH_NAME" >> $GITHUB_OUTPUT

      - name: "Sanitize ENV name"
        id: sanitize_env
        shell: bash
        run: |
          SANITIZED_BRANCH_NAME=$(echo -n ${{ steps.clean-ref.outputs.ref }} | tr "/" "-")
          echo "Sanitized branch name: $SANITIZED_BRANCH_NAME"
          TRIMMED_BRANCH_NAME=$(echo -n "$SANITIZED_BRANCH_NAME" | cut -c 1-18 | sed 's/[-/]$//')
          echo "sanitized_env_name=$SANITIZED_BRANCH_NAME" >> $GITHUB_OUTPUT;
          echo "trimmed_env_name=$TRIMMED_BRANCH_NAME" >> $GITHUB_OUTPUT;
        
      - name: Environment deployment
        id: env-name
        run: |
          echo "deploying environment"
          echo "PR_ENV_NAME=${{ steps.sanitize_env.outputs.trimmed_env_name }}" >> $GITHUB_ENV
          echo "PR_ENV_NAME=${{ steps.sanitize_env.outputs.trimmed_env_name }}" >> $GITHUB_OUTPUT

  destroy-preview:
    needs: deploy-dev-pr-environment
    if: |
      (github.event_name == 'pull_request' && github.event.action == 'unlabeled' && github.event.label.name == 'deploy-pr') 
      ||
      (github.event_name == 'pull_request' && github.event.action == 'closed' && contains(github.event.pull_request.labels.*.name, 'deploy-pr'))
      ||
      github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    steps:
      - name: Trigger workflow in another repo
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GIT_TOKEN }}
          script: |
            try {
              await github.rest.repos.createDispatchEvent({
                owner: 'ballerine-io',
                repo: 'cloud-infra-config',
                event_type: 'destroy-preview',
                client_payload: {
                  'ref': '${{ needs.deploy-dev-pr-environment.outputs.env_name }}'
                }
              });
              console.log('Successfully triggered deploy-preview event');
            } catch (error) {
              console.error('Failed to trigger deploy-preview event:', error);
              throw error;
            }