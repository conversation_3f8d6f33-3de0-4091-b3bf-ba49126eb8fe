"""
Script pour corriger la colonne situation_familiale
Simplifie les situations mariées en une seule catégorie 'marie'
"""

import pandas as pd
import numpy as np
from datetime import datetime

def fix_situation_familiale():
    """Corrige la colonne situation_familiale dans le dataset existant"""
    
    print("=" * 60)
    print("CORRECTION SITUATION FAMILIALE")
    print("=" * 60)
    
    # Charger le dataset nettoyé
    input_file = "output/tunisian_credit_multicategories_100k_clean.csv"
    df = pd.read_csv(input_file)
    
    print(f"📊 Dataset original: {len(df):,} clients")
    
    # Afficher les situations familiales actuelles
    print(f"\n📋 SITUATIONS FAMILIALES ACTUELLES")
    print("-" * 40)
    situations_avant = df['situation_familiale'].value_counts().sort_index()
    for situation, count in situations_avant.items():
        pct = count / len(df) * 100
        print(f"  {situation:<25}: {count:>6,} ({pct:>5.1f}%)")
    
    # Mapper les anciennes situations vers les nouvelles
    print(f"\n🔄 REGROUPEMENT DES SITUATIONS MARIÉES")
    print("-" * 40)
    
    mapping = {
        'marie_sans_enfants': 'marie',
        'marie_1_2_enfants': 'marie', 
        'marie_3_plus_enfants': 'marie',
        'celibataire': 'celibataire',
        'divorce': 'divorce',
        'veuf': 'veuf'
    }
    
    # Appliquer le mapping
    df['situation_familiale_nouvelle'] = df['situation_familiale'].map(mapping)
    
    # Vérifier le mapping
    for ancienne, nouvelle in mapping.items():
        count = (df['situation_familiale'] == ancienne).sum()
        if count > 0:
            print(f"  {ancienne:<25} → {nouvelle:<15}: {count:>6,} clients")
    
    # Remplacer l'ancienne colonne
    df['situation_familiale'] = df['situation_familiale_nouvelle']
    df.drop('situation_familiale_nouvelle', axis=1, inplace=True)
    
    # Afficher les nouvelles situations
    print(f"\n📋 SITUATIONS FAMILIALES APRÈS CORRECTION")
    print("-" * 40)
    situations_apres = df['situation_familiale'].value_counts().sort_index()
    for situation, count in situations_apres.items():
        pct = count / len(df) * 100
        print(f"  {situation:<15}: {count:>6,} ({pct:>5.1f}%)")
    
    # Vérifier la cohérence avec nombre_enfants
    print(f"\n🔍 VÉRIFICATION COHÉRENCE AVEC NOMBRE_ENFANTS")
    print("-" * 40)
    
    # Célibataires avec enfants (peut arriver)
    celibataires_avec_enfants = df[
        (df['situation_familiale'] == 'celibataire') & 
        (df['nombre_enfants'] > 0)
    ]
    print(f"Célibataires avec enfants: {len(celibataires_avec_enfants):,} ({len(celibataires_avec_enfants)/len(df)*100:.1f}%)")
    
    # Mariés sans enfants
    maries_sans_enfants = df[
        (df['situation_familiale'] == 'marie') & 
        (df['nombre_enfants'] == 0)
    ]
    print(f"Mariés sans enfants: {len(maries_sans_enfants):,} ({len(maries_sans_enfants)/len(df)*100:.1f}%)")
    
    # Mariés avec enfants
    maries_avec_enfants = df[
        (df['situation_familiale'] == 'marie') & 
        (df['nombre_enfants'] > 0)
    ]
    print(f"Mariés avec enfants: {len(maries_avec_enfants):,} ({len(maries_avec_enfants)/len(df)*100:.1f}%)")
    
    # Statistiques nombre d'enfants par situation
    print(f"\n📊 NOMBRE D'ENFANTS PAR SITUATION FAMILIALE")
    print("-" * 40)
    for situation in df['situation_familiale'].unique():
        situation_df = df[df['situation_familiale'] == situation]
        enfants_stats = situation_df['nombre_enfants'].describe()
        print(f"\n{situation.capitalize()}:")
        print(f"  Moyenne enfants: {enfants_stats['mean']:.1f}")
        print(f"  Min-Max: {enfants_stats['min']:.0f}-{enfants_stats['max']:.0f}")
        
        # Distribution détaillée
        enfants_dist = situation_df['nombre_enfants'].value_counts().sort_index()
        print(f"  Distribution: ", end="")
        for nb_enfants, count in enfants_dist.head(6).items():
            pct = count / len(situation_df) * 100
            print(f"{nb_enfants}enf({pct:.0f}%) ", end="")
        print()
    
    # Exporter le dataset corrigé
    print(f"\n💾 EXPORT DATASET CORRIGÉ")
    print("-" * 40)
    
    output_file = "output/tunisian_credit_multicategories_100k_clean_fixed.csv"
    df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"✅ Dataset corrigé exporté: {output_file}")
    
    # Remplacer aussi le fichier principal
    df.to_csv(input_file, index=False, encoding='utf-8')
    print(f"✅ Fichier principal mis à jour: {input_file}")
    
    # Créer un rapport de correction
    rapport_correction = {
        'date_correction': datetime.now().isoformat(),
        'type_correction': 'simplification_situation_familiale',
        'clients_traites': len(df),
        'situations_avant': {str(k): int(v) for k, v in situations_avant.items()},
        'situations_apres': {str(k): int(v) for k, v in situations_apres.items()},
        'mapping_applique': mapping,
        'coherence_enfants': {
            'celibataires_avec_enfants': len(celibataires_avec_enfants),
            'maries_sans_enfants': len(maries_sans_enfants),
            'maries_avec_enfants': len(maries_avec_enfants)
        }
    }
    
    import json
    rapport_file = "output/rapport_correction_situation_familiale.json"
    with open(rapport_file, 'w', encoding='utf-8') as f:
        json.dump(rapport_correction, f, indent=2, ensure_ascii=False)
    print(f"✅ Rapport de correction: {rapport_file}")
    
    # Statistiques finales
    print(f"\n📋 RÉSUMÉ DE LA CORRECTION")
    print("-" * 40)
    print(f"✅ Situations mariées regroupées: marie_sans_enfants + marie_1_2_enfants + marie_3_plus_enfants → marie")
    print(f"✅ Redondance éliminée: situation_familiale + nombre_enfants maintenant complémentaires")
    print(f"✅ Structure simplifiée: 4 situations au lieu de 6")
    print(f"✅ Cohérence maintenue avec nombre_enfants")
    
    print(f"\n🎉 CORRECTION TERMINÉE AVEC SUCCÈS!")
    
    return df

if __name__ == "__main__":
    fix_situation_familiale()
