// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`formatBusinessReports as CSV Tests > should format sandbox report correctly 1`] = `
"Merchant ID,Report ID,Merchant Name,Merchant URL,Risk Level,Scan Type,Monitoring Alert,Company Analysis Findings,Company Analysis Description,Company Analysis Source,Website LOB,Website MCC,Violation Type,Why Our AI Flagged this?,Source,Violation URL (if applicable),Website Reputation Findings,Website Reputation Reason,Website Reputation Source,Traffic Findings,Estimated Monthly Visits,Traffic Sources,Time on site,Pages per visit,Bounce rate,Pricing Findings,Pricing Findings Details,Pricing Sources,Website Structure Findings,Ecosystem,Facebook Link,Facebook Details,Instagram Link,Instagram Details,Scan Creation Date,Report Status
cm8772isp04pcs00k8eccnakc,atb5c09dfqf78v1812mvdui8,ForOr,url: https://ballerine.com/,high,Onboarding,No,Negative Company Reputation,"Dollars Markets Ltd has a low trust score and is flagged for concerns regarding its regulatory status, customer complaints, and lack of transparency. The broker operates under a Mauritius FSC license, which is considered weak regulatory oversight.",https://www.wikifx.com/en/dealer/**********.html,Risk Management Platform,6012 - Risk Management Platform,Cryptocurrency,"The website is a trading platform that markets forex and CFD services and explicitly lists cryptocurrencies among its trading instruments. This directly aligns with the trigger conditions for both the 'content-cryptocurrency' violation (trading/sale of digital currencies) and the 'content-securities-trading' violation (offering FX, CFDs, and other asset trading services).","Trading Instruments: Currency Pairs, Precious Metals, Indices, Cryptocurrencies (31 Cryptocurrencies), Share Stocks, Energy & ETFs",https://dollarsmarkets.com/,Listings on Scam Reporting Websites,The website nigoo.store is featured on a scam reporting website with a review questioning its legitimacy.,https://www.scam-detector.com/validator/nigoo-store-review/,Low Traffic Volumes,"2024-08-01: 5353,
2024-09-01: 2900,
2024-10-01: 3987,
2024-11-01: 3220,
2024-12-01: 6623,
2025-01-01: 11895","direct: 0.48233798536743344,
search / organic: 0.35351515572529957",78.2998703996644,1.****************,0.5807437490057886,Unusually High Prices,The listed price of the denim jacket is significantly higher than the typical market price for such items.,https://www.nigoo.store/NIGO-Washed-Old-Short-Vintage-Denim-Jacket-Men-and-Women-Fashion-Blue-Denim-Jacket-Ngvp-nigo6554-p23212559.html,"Missing Terms and Conditions (T&C),
Missing About Us","domain: ballerine.com,
relatedNode: Node,
relatedNodeType: Node",https://facebook.com/IKEAUSA,"id: 85409903065,
url: https://facebook.com/IKEAUSA,
pageName: IKEA,
name: IKEA,
email: <EMAIL>,
address: Some,
phoneNumber: *********,
creationDate: April 16, 2009,
numberOfLikes: ********,
pageCategories: Furniture,
likesCount: ********,
facebookAdsLink: https://www.facebook.com/ads/library/?view_all_page_id=****************,
facebookAboutUsLink: https://facebook.com/IKEAUSA/about",https://www.instagram.com/ikeausa,"id: *********,
url: https://www.instagram.com/ikeausa,
pageName: IKEA USA,
username: ikeausa,
isVerified: true,
biography: Design ideas & solutions to make life at home easier. Share your photos using #MyIKEAUSA 
© Inter IKEA Systems B.V. 2013-2024
Shop our photos:,
postsCount: 4023,
followsCount: 68,
isBusinessAccount: true,
numberOfFollowers: 2543351,
pageCategories: Home Goods Stores",2023-01-01T12:00:00Z,pending-review
cm7rtjl1g0002u10km5qgpzlu,ayhbiklk97r37lld74wkig90,,url: https://www.ebmarket.jp/,critical,Onboarding,No,,,,,,Landing Page Without Active Content,"The website displays only a placeholder page, lacks interactive elements, functional navigation, or meaningful content beyond a basic design.","The webpage's body contains only a single <div> element with the class 'logo' and no additional content, interactive elements, or navigation links, indicating that the website is currently a placeholder or under construction.",https://www.izi1.com/,"Negative Reputation,
Complaints for Unauthorized Charges","The website ebmarket.jp has been flagged as high-risk on scam-detector.com due to deceptive practices and lack of transparency, including red flags such as unrealistic pricing, poor customer service, and unverified business credentials.,
Users on Reddit have reported that ebmarket.jp engages in fraudulent activities, including unauthorized charges and failure to deliver products, alongside multiple complaints about its suspicious payment processing methods.","https://scam-detector.com/ebmarket-jp-review,
https://www.reddit.com/r/scams/comments/xyz123/ebmarket_jp_scam/",Low Traffic Volumes,,,,,,,,,"Missing Terms and Conditions (T&C),
Missing Privacy Policy,
Missing About Us,
Missing Contact Us",,,,,,2023-01-01T12:00:00Z,completed
cm7ohzfoe026aw60kkjg439c4,bix6dnbeui7kjk1d3gf66ggh,,url: https://www.oreglory.com/,high,Onboarding,No,,,,,,"Medical Devices,
Cosmetics","The website lists and promotes several health monitoring gadgets such as a 'Blood Glucose Monitoring Smartwatch' and other smartwatches that monitor blood sugar, blood pressure, and ECG. These products are categorized as medical devices, which trigger the violation for offering medical device sales.,
The website’s beauty category features products such as the Make-Up Mirror, which is a beauty tool. Since the trigger for 'content-cosmetics' covers makeup, skincare products, and beauty tools, this product qualifies under that violation.","Blood Glucose Monitoring Smartwatch | Smart Watch for Non-Invasive Blood Glucose Testing,
Make-Up Mirror – Adjustable Brightness – Dual Magnification – Tri-Color Lighting – Rechargeable – Easy Installation – Ideal for Beauty Routine","https://www.oreglory.com/,
https://www.oreglory.com/product-category/beauty/",,,,Traffic Shows High Bounce Rate,,,,,,,,,"Missing Terms and Conditions (T&C),
Missing About Us",,,,,,2023-01-01T12:00:00Z,quality-control
cm7ka0wt902eout0kqf1j5266,r4czrf1kykffo9qukxtu4pgx,myballerine,url: https://www.myballerine.sg/,medium,Onboarding,No,,,,,,,,,,,,,Low Traffic Volumes,,,,,,,,,Missing Terms and Conditions (T&C),,,,,,2023-01-01T12:00:00Z,quality-control
cm7hs2ne0002mub0kyvee7sc3,auso4n5q5qxb8u6kdi8vin3k,,url: https://jp.nextcigar.com/,high,Onboarding,No,,,,,,Offline Website,,,,,,,,,,,,,,,,,,,,,,2023-01-01T12:00:00Z,quality-control
MID123,iy8p3nt5tmrn523nsf0hbt4s,Saffron Walden,url: https://test2.com/,medium,Onboarding,No,,,,,,,,,,,,,Low Traffic Volumes,,,,,,,,,"Missing Terms and Conditions (T&C),
Missing Privacy Policy,
Missing About Us,
Missing Contact Us",,,,,,2023-01-01T12:00:00Z,quality-control
RobbieAdi,hugpgsfv0xnd4ybkf9c1ufus,,url: https://test.com/,high,Onboarding,No,,,,,,Offline Website,,,,,,,,,,,,,,,,,,,,,,2023-01-01T12:00:00Z,quality-control
cm7g5divx00gcs10krqk2f6q6,mih1tz5dp8w1vyxdzhj9duwy,Folklore Event Rentals,url: https://www.adorefolklore.com/,medium,Onboarding,No,,,,,,,,,,,,,Low Traffic Volumes,,,,,,,,,"Missing Terms and Conditions (T&C),
Missing Privacy Policy",,,,,,2023-01-01T12:00:00Z,quality-control
cm7abgii201msqn0ksi44agfd,k3kdprx5rxya6fah9oho4tqo,,url: https://www.yuliiacouture.com/,medium,Onboarding,No,,,,,,,,,,,,,,,,,,,,,,Missing Terms and Conditions (T&C),,,,,,2023-01-01T12:00:00Z,quality-control"
`;

exports[`formatBusinessReportsForCsv Snapshot Tests > should format ecosystem-report correctly 1`] = `
[
  {
    "Bounce rate": null,
    "Company Analysis Description": [],
    "Company Analysis Findings": [],
    "Company Analysis Source": [],
    "Ecosystem": [
      {
        "domain": "ballerine.com",
        "relatedNode": "Node",
        "relatedNodeType": "Node",
      },
      {
        "domain": "ballerine.io",
        "relatedNode": "<EMAIL>",
        "relatedNodeType": "Email",
      },
    ],
    "Estimated Monthly Visits": null,
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "business-123",
    "Merchant Name": "Test Company",
    "Merchant URL": "https://example.com",
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "report-123",
    "Report Status": "completed",
    "Risk Level": "low",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [],
    "Time on site": null,
    "Traffic Findings": [],
    "Traffic Sources": null,
    "Violation Type": [],
    "Violation URL (if applicable)": [],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [],
    "Website Reputation Reason": [],
    "Website Reputation Source": [],
    "Website Structure Findings": [],
    "Why Our AI Flagged this?": [],
  },
]
`;

exports[`formatBusinessReportsForCsv Snapshot Tests > should format example-report correctly 1`] = `
[
  {
    "Bounce rate": null,
    "Company Analysis Description": [
      "Dollars Markets Ltd has a low trust score and is flagged for concerns regarding its regulatory status, customer complaints, and lack of transparency. The broker operates under a Mauritius FSC license, which is considered weak regulatory oversight.",
    ],
    "Company Analysis Findings": [
      "Negative Company Reputation",
    ],
    "Company Analysis Source": [
      "https://www.wikifx.com/en/dealer/**********.html",
    ],
    "Ecosystem": [],
    "Estimated Monthly Visits": null,
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "cm8772isp04pcs00k8eccnakc",
    "Merchant Name": "ForOr",
    "Merchant URL": {
      "url": "https://ballerine.com/",
    },
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [
      "Unusually High Prices",
    ],
    "Pricing Findings Details": [
      "The listed price of the denim jacket is significantly higher than the typical market price for such items.",
    ],
    "Pricing Sources": [
      "https://www.nigoo.store/NIGO-Washed-Old-Short-Vintage-Denim-Jacket-Men-and-Women-Fashion-Blue-Denim-Jacket-Ngvp-nigo6554-p23212559.html",
    ],
    "Report ID": "atb5c09dfqf78v1812mvdui8",
    "Report Status": "pending-review",
    "Risk Level": "high",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [
      "Trading Instruments: Currency Pairs, Precious Metals, Indices, Cryptocurrencies (31 Cryptocurrencies), Share Stocks, Energy & ETFs",
    ],
    "Time on site": null,
    "Traffic Findings": [
      "Low Traffic Volumes",
    ],
    "Traffic Sources": null,
    "Violation Type": [
      "Cryptocurrency",
    ],
    "Violation URL (if applicable)": [
      "https://dollarsmarkets.com/",
    ],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [
      "Listings on Scam Reporting Websites",
    ],
    "Website Reputation Reason": [
      "The website nigoo.store is featured on a scam reporting website with a review questioning its legitimacy.",
    ],
    "Website Reputation Source": [
      "https://www.scam-detector.com/validator/nigoo-store-review/",
    ],
    "Website Structure Findings": [
      "Missing Terms and Conditions (T&C)",
      "Missing About Us",
    ],
    "Why Our AI Flagged this?": [
      "The website is a trading platform that markets forex and CFD services and explicitly lists cryptocurrencies among its trading instruments. This directly aligns with the trigger conditions for both the 'content-cryptocurrency' violation (trading/sale of digital currencies) and the 'content-securities-trading' violation (offering FX, CFDs, and other asset trading services).",
    ],
  },
]
`;

exports[`formatBusinessReportsForCsv Snapshot Tests > should format mcc-report correctly 1`] = `
[
  {
    "Bounce rate": null,
    "Company Analysis Description": [],
    "Company Analysis Findings": [],
    "Company Analysis Source": [],
    "Ecosystem": [],
    "Estimated Monthly Visits": null,
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "business-123",
    "Merchant Name": "Test Company",
    "Merchant URL": "https://example.com",
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "report-123",
    "Report Status": "completed",
    "Risk Level": "low",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [],
    "Time on site": null,
    "Traffic Findings": [],
    "Traffic Sources": null,
    "Violation Type": [],
    "Violation URL (if applicable)": [],
    "Website LOB": null,
    "Website MCC": "5734 - Computer Software Stores",
    "Website Reputation Findings": [],
    "Website Reputation Reason": [],
    "Website Reputation Source": [],
    "Website Structure Findings": [],
    "Why Our AI Flagged this?": [],
  },
]
`;

exports[`formatBusinessReportsForCsv Snapshot Tests > should format minimal-report correctly 1`] = `
[
  {
    "Bounce rate": null,
    "Company Analysis Description": [],
    "Company Analysis Findings": [],
    "Company Analysis Source": [],
    "Ecosystem": [],
    "Estimated Monthly Visits": null,
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "correlation-123",
    "Merchant Name": "Test Company",
    "Merchant URL": "https://example.com",
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "report-123",
    "Report Status": "completed",
    "Risk Level": "low",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [],
    "Time on site": null,
    "Traffic Findings": [],
    "Traffic Sources": null,
    "Violation Type": [],
    "Violation URL (if applicable)": [],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [],
    "Website Reputation Reason": [],
    "Website Reputation Source": [],
    "Website Structure Findings": [],
    "Why Our AI Flagged this?": [],
  },
]
`;

exports[`formatBusinessReportsForCsv Snapshot Tests > should format sandbox-report correctly 1`] = `
[
  {
    "Bounce rate": 0.5807437490057886,
    "Company Analysis Description": [
      "Dollars Markets Ltd has a low trust score and is flagged for concerns regarding its regulatory status, customer complaints, and lack of transparency. The broker operates under a Mauritius FSC license, which is considered weak regulatory oversight.",
    ],
    "Company Analysis Findings": [
      "Negative Company Reputation",
    ],
    "Company Analysis Source": [
      "https://www.wikifx.com/en/dealer/**********.html",
    ],
    "Ecosystem": [
      {
        "domain": "ballerine.com",
        "relatedNode": "Node",
        "relatedNodeType": "Node",
      },
    ],
    "Estimated Monthly Visits": {
      "2024-08-01": 5353,
      "2024-09-01": 2900,
      "2024-10-01": 3987,
      "2024-11-01": 3220,
      "2024-12-01": 6623,
      "2025-01-01": 11895,
    },
    "Facebook Details": {
      "address": "Some",
      "creationDate": "April 16, 2009",
      "email": "<EMAIL>",
      "facebookAboutUsLink": "https://facebook.com/IKEAUSA/about",
      "facebookAdsLink": "https://www.facebook.com/ads/library/?view_all_page_id=****************",
      "id": "85409903065",
      "likesCount": ********,
      "name": "IKEA",
      "numberOfLikes": ********,
      "pageCategories": "Furniture",
      "pageName": "IKEA",
      "phoneNumber": "*********",
      "url": "https://facebook.com/IKEAUSA",
    },
    "Facebook Link": "https://facebook.com/IKEAUSA",
    "Instagram Details": {
      "biography": "Design ideas & solutions to make life at home easier. Share your photos using #MyIKEAUSA 
© Inter IKEA Systems B.V. 2013-2024
Shop our photos:",
      "followsCount": 68,
      "id": "*********",
      "isBusinessAccount": true,
      "isVerified": true,
      "numberOfFollowers": 2543351,
      "pageCategories": "Home Goods Stores",
      "pageName": "IKEA USA",
      "postsCount": 4023,
      "url": "https://www.instagram.com/ikeausa",
      "username": "ikeausa",
    },
    "Instagram Link": "https://www.instagram.com/ikeausa",
    "Merchant ID": "cm8772isp04pcs00k8eccnakc",
    "Merchant Name": "ForOr",
    "Merchant URL": {
      "url": "https://ballerine.com/",
    },
    "Monitoring Alert": "No",
    "Pages per visit": 1.****************,
    "Pricing Findings": [
      "Unusually High Prices",
    ],
    "Pricing Findings Details": [
      "The listed price of the denim jacket is significantly higher than the typical market price for such items.",
    ],
    "Pricing Sources": [
      "https://www.nigoo.store/NIGO-Washed-Old-Short-Vintage-Denim-Jacket-Men-and-Women-Fashion-Blue-Denim-Jacket-Ngvp-nigo6554-p23212559.html",
    ],
    "Report ID": "atb5c09dfqf78v1812mvdui8",
    "Report Status": "pending-review",
    "Risk Level": "high",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [
      "Trading Instruments: Currency Pairs, Precious Metals, Indices, Cryptocurrencies (31 Cryptocurrencies), Share Stocks, Energy & ETFs",
    ],
    "Time on site": 78.2998703996644,
    "Traffic Findings": [
      "Low Traffic Volumes",
    ],
    "Traffic Sources": {
      "direct": 0.48233798536743344,
      "search / organic": 0.35351515572529957,
    },
    "Violation Type": [
      "Cryptocurrency",
    ],
    "Violation URL (if applicable)": [
      "https://dollarsmarkets.com/",
    ],
    "Website LOB": "Risk Management Platform",
    "Website MCC": "6012 - Risk Management Platform",
    "Website Reputation Findings": [
      "Listings on Scam Reporting Websites",
    ],
    "Website Reputation Reason": [
      "The website nigoo.store is featured on a scam reporting website with a review questioning its legitimacy.",
    ],
    "Website Reputation Source": [
      "https://www.scam-detector.com/validator/nigoo-store-review/",
    ],
    "Website Structure Findings": [
      "Missing Terms and Conditions (T&C)",
      "Missing About Us",
    ],
    "Why Our AI Flagged this?": [
      "The website is a trading platform that markets forex and CFD services and explicitly lists cryptocurrencies among its trading instruments. This directly aligns with the trigger conditions for both the 'content-cryptocurrency' violation (trading/sale of digital currencies) and the 'content-securities-trading' violation (offering FX, CFDs, and other asset trading services).",
    ],
  },
  {
    "Bounce rate": null,
    "Company Analysis Description": [],
    "Company Analysis Findings": [],
    "Company Analysis Source": [],
    "Ecosystem": [],
    "Estimated Monthly Visits": null,
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "cm7rtjl1g0002u10km5qgpzlu",
    "Merchant Name": null,
    "Merchant URL": {
      "url": "https://www.ebmarket.jp/",
    },
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "ayhbiklk97r37lld74wkig90",
    "Report Status": "completed",
    "Risk Level": "critical",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [
      "The webpage's body contains only a single <div> element with the class 'logo' and no additional content, interactive elements, or navigation links, indicating that the website is currently a placeholder or under construction.",
    ],
    "Time on site": null,
    "Traffic Findings": [
      "Low Traffic Volumes",
    ],
    "Traffic Sources": null,
    "Violation Type": [
      "Landing Page Without Active Content",
    ],
    "Violation URL (if applicable)": [
      "https://www.izi1.com/",
    ],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [
      "Negative Reputation",
      "Complaints for Unauthorized Charges",
    ],
    "Website Reputation Reason": [
      "The website ebmarket.jp has been flagged as high-risk on scam-detector.com due to deceptive practices and lack of transparency, including red flags such as unrealistic pricing, poor customer service, and unverified business credentials.",
      "Users on Reddit have reported that ebmarket.jp engages in fraudulent activities, including unauthorized charges and failure to deliver products, alongside multiple complaints about its suspicious payment processing methods.",
    ],
    "Website Reputation Source": [
      "https://scam-detector.com/ebmarket-jp-review",
      "https://www.reddit.com/r/scams/comments/xyz123/ebmarket_jp_scam/",
    ],
    "Website Structure Findings": [
      "Missing Terms and Conditions (T&C)",
      "Missing Privacy Policy",
      "Missing About Us",
      "Missing Contact Us",
    ],
    "Why Our AI Flagged this?": [
      "The website displays only a placeholder page, lacks interactive elements, functional navigation, or meaningful content beyond a basic design.",
    ],
  },
  {
    "Bounce rate": null,
    "Company Analysis Description": [],
    "Company Analysis Findings": [],
    "Company Analysis Source": [],
    "Ecosystem": [],
    "Estimated Monthly Visits": null,
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "cm7ohzfoe026aw60kkjg439c4",
    "Merchant Name": null,
    "Merchant URL": {
      "url": "https://www.oreglory.com/",
    },
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "bix6dnbeui7kjk1d3gf66ggh",
    "Report Status": "quality-control",
    "Risk Level": "high",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [
      "Blood Glucose Monitoring Smartwatch | Smart Watch for Non-Invasive Blood Glucose Testing",
      "Make-Up Mirror – Adjustable Brightness – Dual Magnification – Tri-Color Lighting – Rechargeable – Easy Installation – Ideal for Beauty Routine",
    ],
    "Time on site": null,
    "Traffic Findings": [
      "Traffic Shows High Bounce Rate",
    ],
    "Traffic Sources": null,
    "Violation Type": [
      "Medical Devices",
      "Cosmetics",
    ],
    "Violation URL (if applicable)": [
      "https://www.oreglory.com/",
      "https://www.oreglory.com/product-category/beauty/",
    ],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [],
    "Website Reputation Reason": [],
    "Website Reputation Source": [],
    "Website Structure Findings": [
      "Missing Terms and Conditions (T&C)",
      "Missing About Us",
    ],
    "Why Our AI Flagged this?": [
      "The website lists and promotes several health monitoring gadgets such as a 'Blood Glucose Monitoring Smartwatch' and other smartwatches that monitor blood sugar, blood pressure, and ECG. These products are categorized as medical devices, which trigger the violation for offering medical device sales.",
      "The website’s beauty category features products such as the Make-Up Mirror, which is a beauty tool. Since the trigger for 'content-cosmetics' covers makeup, skincare products, and beauty tools, this product qualifies under that violation.",
    ],
  },
  {
    "Bounce rate": null,
    "Company Analysis Description": [],
    "Company Analysis Findings": [],
    "Company Analysis Source": [],
    "Ecosystem": [],
    "Estimated Monthly Visits": null,
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "cm7ka0wt902eout0kqf1j5266",
    "Merchant Name": "myballerine",
    "Merchant URL": {
      "url": "https://www.myballerine.sg/",
    },
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "r4czrf1kykffo9qukxtu4pgx",
    "Report Status": "quality-control",
    "Risk Level": "medium",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [],
    "Time on site": null,
    "Traffic Findings": [
      "Low Traffic Volumes",
    ],
    "Traffic Sources": null,
    "Violation Type": [],
    "Violation URL (if applicable)": [],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [],
    "Website Reputation Reason": [],
    "Website Reputation Source": [],
    "Website Structure Findings": [
      "Missing Terms and Conditions (T&C)",
    ],
    "Why Our AI Flagged this?": [],
  },
  {
    "Bounce rate": null,
    "Company Analysis Description": [],
    "Company Analysis Findings": [],
    "Company Analysis Source": [],
    "Ecosystem": [],
    "Estimated Monthly Visits": null,
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "cm7hs2ne0002mub0kyvee7sc3",
    "Merchant Name": null,
    "Merchant URL": {
      "url": "https://jp.nextcigar.com/",
    },
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "auso4n5q5qxb8u6kdi8vin3k",
    "Report Status": "quality-control",
    "Risk Level": "high",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [],
    "Time on site": null,
    "Traffic Findings": [],
    "Traffic Sources": null,
    "Violation Type": [
      "Offline Website",
    ],
    "Violation URL (if applicable)": [],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [],
    "Website Reputation Reason": [],
    "Website Reputation Source": [],
    "Website Structure Findings": [],
    "Why Our AI Flagged this?": [],
  },
  {
    "Bounce rate": null,
    "Company Analysis Description": [],
    "Company Analysis Findings": [],
    "Company Analysis Source": [],
    "Ecosystem": [],
    "Estimated Monthly Visits": null,
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "MID123",
    "Merchant Name": "Saffron Walden",
    "Merchant URL": {
      "url": "https://test2.com/",
    },
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "iy8p3nt5tmrn523nsf0hbt4s",
    "Report Status": "quality-control",
    "Risk Level": "medium",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [],
    "Time on site": null,
    "Traffic Findings": [
      "Low Traffic Volumes",
    ],
    "Traffic Sources": null,
    "Violation Type": [],
    "Violation URL (if applicable)": [],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [],
    "Website Reputation Reason": [],
    "Website Reputation Source": [],
    "Website Structure Findings": [
      "Missing Terms and Conditions (T&C)",
      "Missing Privacy Policy",
      "Missing About Us",
      "Missing Contact Us",
    ],
    "Why Our AI Flagged this?": [],
  },
  {
    "Bounce rate": null,
    "Company Analysis Description": [],
    "Company Analysis Findings": [],
    "Company Analysis Source": [],
    "Ecosystem": [],
    "Estimated Monthly Visits": null,
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "RobbieAdi",
    "Merchant Name": null,
    "Merchant URL": {
      "url": "https://test.com/",
    },
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "hugpgsfv0xnd4ybkf9c1ufus",
    "Report Status": "quality-control",
    "Risk Level": "high",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [],
    "Time on site": null,
    "Traffic Findings": [],
    "Traffic Sources": null,
    "Violation Type": [
      "Offline Website",
    ],
    "Violation URL (if applicable)": [],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [],
    "Website Reputation Reason": [],
    "Website Reputation Source": [],
    "Website Structure Findings": [],
    "Why Our AI Flagged this?": [],
  },
  {
    "Bounce rate": null,
    "Company Analysis Description": [],
    "Company Analysis Findings": [],
    "Company Analysis Source": [],
    "Ecosystem": [],
    "Estimated Monthly Visits": null,
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "cm7g5divx00gcs10krqk2f6q6",
    "Merchant Name": "Folklore Event Rentals",
    "Merchant URL": {
      "url": "https://www.adorefolklore.com/",
    },
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "mih1tz5dp8w1vyxdzhj9duwy",
    "Report Status": "quality-control",
    "Risk Level": "medium",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [],
    "Time on site": null,
    "Traffic Findings": [
      "Low Traffic Volumes",
    ],
    "Traffic Sources": null,
    "Violation Type": [],
    "Violation URL (if applicable)": [],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [],
    "Website Reputation Reason": [],
    "Website Reputation Source": [],
    "Website Structure Findings": [
      "Missing Terms and Conditions (T&C)",
      "Missing Privacy Policy",
    ],
    "Why Our AI Flagged this?": [],
  },
  {
    "Bounce rate": null,
    "Company Analysis Description": [],
    "Company Analysis Findings": [],
    "Company Analysis Source": [],
    "Ecosystem": [],
    "Estimated Monthly Visits": null,
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "cm7abgii201msqn0ksi44agfd",
    "Merchant Name": null,
    "Merchant URL": {
      "url": "https://www.yuliiacouture.com/",
    },
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "k3kdprx5rxya6fah9oho4tqo",
    "Report Status": "quality-control",
    "Risk Level": "medium",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [],
    "Time on site": null,
    "Traffic Findings": [],
    "Traffic Sources": null,
    "Violation Type": [],
    "Violation URL (if applicable)": [],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [],
    "Website Reputation Reason": [],
    "Website Reputation Source": [],
    "Website Structure Findings": [
      "Missing Terms and Conditions (T&C)",
    ],
    "Why Our AI Flagged this?": [],
  },
]
`;

exports[`formatBusinessReportsForCsv Snapshot Tests > should format social-media-report correctly 1`] = `
[
  {
    "Bounce rate": null,
    "Company Analysis Description": [],
    "Company Analysis Findings": [],
    "Company Analysis Source": [],
    "Ecosystem": [],
    "Estimated Monthly Visits": null,
    "Facebook Details": {
      "created": "2020-01-01",
      "followers": "10K",
      "url": "https://facebook.com/testcompany",
    },
    "Facebook Link": "https://facebook.com/testcompany",
    "Instagram Details": {
      "created": "2021-01-01",
      "followers": "5K",
      "url": "https://instagram.com/testcompany",
    },
    "Instagram Link": "https://instagram.com/testcompany",
    "Merchant ID": "business-123",
    "Merchant Name": "Test Company",
    "Merchant URL": "https://example.com",
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "report-123",
    "Report Status": "completed",
    "Risk Level": "low",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [],
    "Time on site": null,
    "Traffic Findings": [],
    "Traffic Sources": null,
    "Violation Type": [],
    "Violation URL (if applicable)": [],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [],
    "Website Reputation Reason": [],
    "Website Reputation Source": [],
    "Website Structure Findings": [],
    "Why Our AI Flagged this?": [],
  },
]
`;

exports[`formatBusinessReportsForCsv Snapshot Tests > should format status-notes-report correctly 1`] = `
[
  {
    "Bounce rate": null,
    "Company Analysis Description": [],
    "Company Analysis Findings": [],
    "Company Analysis Source": [],
    "Ecosystem": [],
    "Estimated Monthly Visits": null,
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "business-123",
    "Merchant Name": "Test Company",
    "Merchant URL": "https://example.com",
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "report-123",
    "Report Status": "rejected",
    "Risk Level": "low",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [],
    "Time on site": null,
    "Traffic Findings": [],
    "Traffic Sources": null,
    "Violation Type": [],
    "Violation URL (if applicable)": [],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [],
    "Website Reputation Reason": [],
    "Website Reputation Source": [],
    "Website Structure Findings": [],
    "Why Our AI Flagged this?": [],
  },
]
`;

exports[`formatBusinessReportsForCsv Snapshot Tests > should format traffic-report correctly 1`] = `
[
  {
    "Bounce rate": null,
    "Company Analysis Description": [],
    "Company Analysis Findings": [],
    "Company Analysis Source": [],
    "Ecosystem": [],
    "Estimated Monthly Visits": "50K",
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "business-123",
    "Merchant Name": "Test Company",
    "Merchant URL": "https://example.com",
    "Monitoring Alert": "No",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "report-123",
    "Report Status": "completed",
    "Risk Level": "low",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [],
    "Time on site": null,
    "Traffic Findings": [],
    "Traffic Sources": {
      "direct": 45,
      "search / organic": 30,
    },
    "Violation Type": [],
    "Violation URL (if applicable)": [],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [],
    "Website Reputation Reason": [],
    "Website Reputation Source": [],
    "Website Structure Findings": [],
    "Why Our AI Flagged this?": [],
  },
]
`;

exports[`formatBusinessReportsForCsv Snapshot Tests > should format violations-report correctly 1`] = `
[
  {
    "Bounce rate": null,
    "Company Analysis Description": [
      "Found issue with company registration",
    ],
    "Company Analysis Findings": [
      "Company Issue 1",
    ],
    "Company Analysis Source": [
      "https://company-registry.example.com",
    ],
    "Ecosystem": [],
    "Estimated Monthly Visits": null,
    "Facebook Details": null,
    "Facebook Link": null,
    "Instagram Details": null,
    "Instagram Link": null,
    "Merchant ID": "business-123",
    "Merchant Name": "Test Company",
    "Merchant URL": "https://example.com",
    "Monitoring Alert": "Yes",
    "Pages per visit": null,
    "Pricing Findings": [],
    "Pricing Findings Details": [],
    "Pricing Sources": [],
    "Report ID": "report-123",
    "Report Status": "completed",
    "Risk Level": "medium",
    "Scan Creation Date": "2023-01-01T12:00:00.000Z",
    "Scan Type": "Onboarding",
    "Source": [
      "Prohibited text example",
    ],
    "Time on site": null,
    "Traffic Findings": [],
    "Traffic Sources": null,
    "Violation Type": [
      "Content Issue 1",
    ],
    "Violation URL (if applicable)": [
      "https://content.example.com",
    ],
    "Website LOB": null,
    "Website MCC": null,
    "Website Reputation Findings": [
      "Scam Warning",
    ],
    "Website Reputation Reason": [
      "Signs of potential fraud",
    ],
    "Website Reputation Source": [
      "https://scam-alerts.example.com",
    ],
    "Website Structure Findings": [],
    "Why Our AI Flagged this?": [
      "Found prohibited content",
    ],
  },
]
`;
