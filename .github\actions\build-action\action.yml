name: Build
description: Build the project

runs:
  using: composite
  steps:
    - name: Install Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 18.12.1

    - name: Install pnpm
      uses: pnpm/action-setup@v2
      id: pnpm-install
      with:
        version: 8
        run_install: false

    - name: Get pnpm store directory
      id: pnpm-cache
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

    - uses: actions/cache@v4
      name: Setup pnpm cache
      with:
        path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Install dependencies
      shell: bash
      run: pnpm install

    - name: Build
      shell: bash
      run: pnpm build
