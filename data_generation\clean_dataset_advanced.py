"""
Nettoyage avancé du dataset pour éliminer tous les problèmes
qui peuvent affecter négativement la modélisation
"""

import pandas as pd
import numpy as np
from datetime import datetime

def clean_dataset_advanced():
    """Nettoyage avancé du dataset"""

    print("=" * 80)
    print("NETTOYAGE AVANCÉ DATASET - OPTIMISATION MODÉLISATION")
    print("=" * 80)

    # Charger le dataset
    df = pd.read_csv("output/tunisian_credit_multicategories_100k_clean.csv")
    print(f"📊 Dataset initial: {len(df):,} clients, {len(df.columns)} colonnes")

    # 1. SUPPRESSION COLONNES INUTILES/REDONDANTES
    print(f"\n🗑️ 1. SUPPRESSION COLONNES PROBLÉMATIQUES")
    print("-" * 50)

    colonnes_a_supprimer = [
        # Colonnes quasi-constantes (inutiles)
        'situation_contentieux',  # 100% False
        'default_flag',          # 99.5% False

        # Corrélations parfaites (redondantes)
        'patrimoine_total',      # = valeur_immobilier + valeur_vehicule + epargne
        'garanties_disponibles', # = patrimoine_total
        'valeur_garanties',      # = patrimoine_total
        'dette_totale',          # = dette_immobiliere + dette_auto + dette_personnelle
        'reste_a_vivre',         # Calculé et incohérent
        'score_ead',             # = montant_demande (corrélation 0.997)

        # Colonnes métier problématiques
        'data_quality_score',    # Score artificiel
    ]

    print(f"Colonnes à supprimer ({len(colonnes_a_supprimer)}):")
    for col in colonnes_a_supprimer:
        if col in df.columns:
            print(f"  ❌ {col}")
            df.drop(col, axis=1, inplace=True)
        else:
            print(f"  ⚠️ {col} (déjà absente)")

    print(f"✅ Colonnes après suppression: {len(df.columns)}")

    # 2. CORRECTION INCOHÉRENCES MÉTIER
    print(f"\n🔧 2. CORRECTION INCOHÉRENCES MÉTIER")
    print("-" * 50)

    # Ancienneté emploi > âge - 16
    incoherent_emploi = df['anciennete_emploi'] > (df['age'] - 16)
    nb_corrections_emploi = incoherent_emploi.sum()
    if nb_corrections_emploi > 0:
        print(f"Correction ancienneté emploi: {nb_corrections_emploi:,} cas")
        # Limiter l'ancienneté à âge - 16
        df.loc[incoherent_emploi, 'anciennete_emploi'] = df.loc[incoherent_emploi, 'age'] - 16

    # Revenus négatifs ou nuls
    revenus_negatifs = df['revenu_mensuel'] <= 0
    nb_revenus_negatifs = revenus_negatifs.sum()
    if nb_revenus_negatifs > 0:
        print(f"Correction revenus ≤ 0: {nb_revenus_negatifs:,} cas")
        df.loc[revenus_negatifs, 'revenu_mensuel'] = 500  # SMIG tunisien

    # Ratio endettement extrême (> 500%)
    endettement_extreme = df['ratio_endettement'] > 5.0
    nb_endettement_extreme = endettement_extreme.sum()
    if nb_endettement_extreme > 0:
        print(f"Correction ratio endettement > 500%: {nb_endettement_extreme:,} cas")
        df.loc[endettement_extreme, 'ratio_endettement'] = 5.0  # Plafonner à 500%

    # 3. TRAITEMENT VALEURS ABERRANTES EXTRÊMES
    print(f"\n📊 3. TRAITEMENT VALEURS ABERRANTES")
    print("-" * 50)

    # Fonction pour traiter les aberrants par winsorization
    def winsorize_column(col_name, lower_pct=0.01, upper_pct=0.99):
        if col_name in df.columns:
            lower_bound = df[col_name].quantile(lower_pct)
            upper_bound = df[col_name].quantile(upper_pct)

            aberrants_bas = df[col_name] < lower_bound
            aberrants_haut = df[col_name] > upper_bound

            nb_corrections = aberrants_bas.sum() + aberrants_haut.sum()
            if nb_corrections > 0:
                df.loc[aberrants_bas, col_name] = lower_bound
                df.loc[aberrants_haut, col_name] = upper_bound
                print(f"  Winsorization {col_name}: {nb_corrections:,} valeurs corrigées")

    # Appliquer winsorization aux colonnes problématiques
    colonnes_winsorize = [
        'autres_revenus',
        'dette_immobiliere',
        'dette_auto',
        'dette_personnelle',
        'anciennete_relation_bancaire',
        'apport_personnel',
        'perte_attendue'
    ]

    for col in colonnes_winsorize:
        winsorize_column(col)

    # 4. RECALCUL VARIABLES DÉRIVÉES COHÉRENTES
    print(f"\n🔄 4. RECALCUL VARIABLES DÉRIVÉES")
    print("-" * 50)

    # Recalculer revenu_total
    if all(col in df.columns for col in ['revenu_mensuel', 'autres_revenus']):
        df['revenu_total'] = df['revenu_mensuel'] + df['autres_revenus']
        print("✅ revenu_total recalculé")

    # Recalculer ratio_endettement cohérent
    if all(col in df.columns for col in ['dette_immobiliere', 'dette_auto', 'dette_personnelle', 'revenu_mensuel']):
        dette_totale_recalculee = df['dette_immobiliere'] + df['dette_auto'] + df['dette_personnelle']
        df['ratio_endettement'] = np.where(
            df['revenu_mensuel'] > 0,
            dette_totale_recalculee / df['revenu_mensuel'],
            0
        )
        print("✅ ratio_endettement recalculé")

    # Recalculer capacité de remboursement
    if all(col in df.columns for col in ['revenu_total', 'ratio_endettement']):
        df['capacite_remboursement'] = df['revenu_total'] * 0.33  # 33% du revenu max
        print("✅ capacite_remboursement recalculé")

    # 5. VALIDATION COHÉRENCE FINALE
    print(f"\n✅ 5. VALIDATION COHÉRENCE FINALE")
    print("-" * 50)

    # Vérifications finales
    checks = []

    # Âges cohérents
    ages_ok = ((df['age'] >= 18) & (df['age'] <= 80)).all()
    checks.append(('Âges 18-80 ans', ages_ok))

    # Revenus positifs
    revenus_ok = (df['revenu_mensuel'] > 0).all()
    checks.append(('Revenus positifs', revenus_ok))

    # Ancienneté emploi cohérente
    anciennete_ok = (df['anciennete_emploi'] <= (df['age'] - 16)).all()
    checks.append(('Ancienneté emploi cohérente', anciennete_ok))

    # Scores PD dans [0,1]
    if 'score_pd' in df.columns:
        pd_ok = ((df['score_pd'] >= 0) & (df['score_pd'] <= 1)).all()
        checks.append(('Scores PD [0,1]', pd_ok))

    for check_name, is_ok in checks:
        status = "✅" if is_ok else "❌"
        print(f"  {status} {check_name}")

    # 6. OPTIMISATION TYPES DE DONNÉES
    print(f"\n🎯 6. OPTIMISATION TYPES DE DONNÉES")
    print("-" * 50)

    # Convertir booléens
    bool_columns = ['sexe']  # M/F peut être encodé
    for col in bool_columns:
        if col in df.columns and df[col].dtype == 'object':
            if col == 'sexe':
                df[col] = (df[col] == 'M').astype(int)  # M=1, F=0
                print(f"✅ {col} encodé (M=1, F=0)")

    # Optimiser types numériques
    for col in df.select_dtypes(include=['float64']).columns:
        if df[col].min() >= 0 and df[col].max() < 2**31:
            df[col] = df[col].astype('float32')

    for col in df.select_dtypes(include=['int64']).columns:
        if df[col].min() >= 0 and df[col].max() < 2**15:
            df[col] = df[col].astype('int16')
        elif df[col].min() >= -2**31 and df[col].max() < 2**31:
            df[col] = df[col].astype('int32')

    print(f"✅ Types de données optimisés")

    # 7. EXPORT DATASET NETTOYÉ
    print(f"\n💾 7. EXPORT DATASET OPTIMISÉ")
    print("-" * 50)

    output_file = "output/tunisian_credit_multicategories_100k_optimized.csv"
    df.to_csv(output_file, index=False, encoding='utf-8')

    # Statistiques finales
    taille_mb = df.memory_usage(deep=True).sum() / 1024**2

    print(f"✅ Dataset optimisé exporté: {output_file}")
    print(f"📊 Statistiques finales:")
    print(f"   Clients: {len(df):,}")
    print(f"   Colonnes: {len(df.columns)} (supprimé {len(colonnes_a_supprimer)})")
    print(f"   Taille mémoire: {taille_mb:.1f} MB")
    print(f"   Corrections appliquées: {nb_corrections_emploi + nb_revenus_negatifs + nb_endettement_extreme:,}")

    # Créer rapport de nettoyage
    rapport = {
        'date_nettoyage': datetime.now().isoformat(),
        'type_nettoyage': 'nettoyage_avance_modelisation',
        'clients_finaux': int(len(df)),
        'colonnes_finales': int(len(df.columns)),
        'colonnes_supprimees': colonnes_a_supprimer,
        'corrections_appliquees': {
            'anciennete_emploi': int(nb_corrections_emploi),
            'revenus_negatifs': int(nb_revenus_negatifs) if 'nb_revenus_negatifs' in locals() else 0,
            'endettement_extreme': int(nb_endettement_extreme) if 'nb_endettement_extreme' in locals() else 0
        },
        'winsorization_appliquee': colonnes_winsorize,
        'variables_recalculees': ['revenu_total', 'ratio_endettement', 'capacite_remboursement'],
        'taille_finale_mb': float(taille_mb)
    }

    import json
    rapport_file = "output/rapport_nettoyage_avance.json"
    with open(rapport_file, 'w', encoding='utf-8') as f:
        json.dump(rapport, f, indent=2, ensure_ascii=False)

    print(f"✅ Rapport détaillé: {rapport_file}")

    print(f"\n🎉 NETTOYAGE AVANCÉ TERMINÉ!")
    print(f"Dataset prêt pour modélisation optimale")

    return df

if __name__ == "__main__":
    clean_dataset_advanced()
