# Dataset Synthétique Tunisien pour Scoring de Crédit

## 📋 Description

Ce dataset synthétique contient **100,000 clients tunisiens** avec leurs profils complets pour l'entraînement de modèles de scoring de crédit. Les données sont générées selon les caractéristiques réelles du marché tunisien et respectent les réglementations BCT (Banque Centrale de Tunisie) et CIBCT.

## 🎯 Objectif

Développer un système de scoring de crédit IA pour les banques tunisiennes avec :
- Modèles PD (Probability of Default), LGD (Loss Given Default), EAD (Exposure at Default)
- Classifications de risque C0-C5 selon BCT
- Explications SHAP pour la transparence
- Préparation pour intégration avec données réelles BCT/CIBCT

## 📊 Contenu du Dataset

### Fichiers Principaux
- `tunisian_credit_dataset_100k.csv` - Dataset complet (100k clients, 56 variables)
- `tunisian_credit_sample_1k.csv` - Échantillon pour tests (1k clients)
- `dataset_metadata.json` - Métadonnées et statistiques
- `generation_report.txt` - Rapport détaillé de génération

### Fichiers par Classe de Risque
- `tunisian_credit_classe_C0.csv` - Clients sans risque (35,870 clients)
- `tunisian_credit_classe_C1.csv` - Risque faible (25,919 clients)
- `tunisian_credit_classe_C2.csv` - Risque modéré (30,843 clients)
- `tunisian_credit_classe_C3.csv` - Risque élevé (7,124 clients)
- `tunisian_credit_classe_C4.csv` - Risque très élevé (238 clients)
- `tunisian_credit_classe_C5.csv` - Défaut probable (6 clients)

## 🏗️ Structure des Données (56 Variables)

### 1. Profil Démographique (15 variables)
- `client_id` - Identifiant unique
- `cin` - Numéro CIN tunisien
- `age` - Âge (25-65 ans)
- `sexe` - Genre (M/F)
- `situation_familiale` - État civil
- `nombre_enfants` - Nombre d'enfants
- `niveau_education` - Niveau d'éducation
- `region` - Région tunisienne
- `profession` - Profession
- `secteur_activite` - Secteur d'activité
- `anciennete_emploi` - Ancienneté emploi (mois)
- `type_contrat` - Type de contrat
- `type_logement` - Type de logement
- `anciennete_logement` - Ancienneté logement (mois)
- `taux_chomage_sectoriel` - Taux de chômage du secteur

### 2. Données Financières (15 variables)
- `revenu_mensuel` - Revenu mensuel (TND)
- `autres_revenus` - Autres revenus (TND)
- `revenu_total` - Revenu total (TND)
- `valeur_immobilier` - Valeur immobilière (TND)
- `valeur_vehicule` - Valeur véhicule (TND)
- `epargne` - Épargne (TND)
- `patrimoine_total` - Patrimoine total (TND)
- `dette_immobiliere` - Dette immobilière (TND)
- `dette_auto` - Dette automobile (TND)
- `dette_personnelle` - Dette personnelle (TND)
- `dette_totale` - Dette totale (TND)
- `ratio_endettement` - Ratio d'endettement
- `reste_a_vivre` - Reste à vivre (TND)
- `capacite_remboursement` - Capacité de remboursement (TND)
- `garanties_disponibles` - Garanties disponibles (TND)

### 3. Historique de Crédit (10 variables)
- `nombre_credits_anterieurs` - Nombre de crédits antérieurs
- `anciennete_relation_bancaire` - Ancienneté relation bancaire (mois)
- `banque_principale` - Banque principale
- `retard_maximum_jours` - Retard maximum (jours)
- `nombre_incidents_12m` - Incidents 12 derniers mois
- `nombre_demandes_6m` - Demandes 6 derniers mois
- `taux_utilisation_credit` - Taux d'utilisation crédit
- `regularite_paiements` - Score régularité paiements
- `nombre_rejets_12m` - Rejets 12 derniers mois
- `score_comportement` - Score comportement global

### 4. Demande de Crédit (8 variables)
- `montant_demande` - Montant demandé (TND)
- `duree_demande` - Durée demandée (mois)
- `type_credit` - Type de crédit
- `mensualite_demandee` - Mensualité demandée (TND)
- `taux_propose` - Taux proposé
- `ratio_mensualite_revenu` - Ratio mensualité/revenu
- `apport_personnel` - Apport personnel (TND)
- `valeur_garanties` - Valeur des garanties (TND)

### 5. Variables Cibles et Classifications (8 variables)
- `classe_risque` - Classe de risque BCT (C0-C5)
- `situation_contentieux` - Flag SED (Situation En Difficulté)
- `default_flag` - Variable cible principale (défaut)
- `score_pd` - Score PD (Probability of Default)
- `score_lgd` - Score LGD (Loss Given Default)
- `score_ead` - Score EAD (Exposure at Default)
- `perte_attendue` - Perte attendue (Expected Loss)
- `decision_finale` - Décision finale (APPROVE/REJECT/MANUAL_REVIEW)

## 📈 Statistiques Clés

### Répartition des Décisions
- **REJECT**: 50,959 (51.0%)
- **APPROVE**: 27,596 (27.6%)
- **MANUAL_REVIEW**: 21,445 (21.4%)

### Classes de Risque BCT
- **C0** (Aucun risque): 35,870 (35.9%)
- **C1** (Risque faible): 25,919 (25.9%)
- **C2** (Risque modéré): 30,843 (30.8%)
- **C3** (Risque élevé): 7,124 (7.1%)
- **C4** (Risque très élevé): 238 (0.2%)
- **C5** (Défaut probable): 6 (0.0%)

### Indicateurs Financiers
- **Revenu médian**: 1,467 TND
- **Revenu moyen**: 1,788 TND
- **Montant demandé moyen**: 47,691 TND
- **Score PD moyen**: 0.104
- **Ratio d'endettement moyen**: 94.5%

### Répartition Géographique
- **Autres régions**: 43,755 (43.8%)
- **Tunis**: 25,118 (25.1%)
- **Sfax**: 12,158 (12.2%)
- **Sousse**: 7,967 (8.0%)
- **Kairouan**: 6,026 (6.0%)
- **Gafsa**: 4,976 (5.0%)

## 🔧 Utilisation

### Chargement des Données
```python
import pandas as pd

# Charger le dataset complet
df = pd.read_csv('output/tunisian_credit_dataset_100k.csv')

# Ou charger un échantillon pour tests
df_sample = pd.read_csv('output/tunisian_credit_sample_1k.csv')
```

### Variables Cibles
- **Classification**: `classe_risque` (C0-C5)
- **Défaut binaire**: `default_flag` (True/False)
- **Décision**: `decision_finale` (APPROVE/REJECT/MANUAL_REVIEW)

### Modélisation Recommandée
1. **Modèle PD**: Prédire `score_pd` ou `default_flag`
2. **Modèle LGD**: Prédire `score_lgd` pour les défauts
3. **Modèle EAD**: Prédire `score_ead` pour l'exposition
4. **Classification BCT**: Prédire `classe_risque`

## ✅ Qualité des Données

- ✅ **0 valeurs manquantes**
- ✅ **0 doublons**
- ✅ **Cohérence métier respectée**
- ✅ **Corrélations réalistes**
- ✅ **Distributions conformes au marché tunisien**

## 🚀 Prochaines Étapes

1. **Modélisation ML**: XGBoost, Random Forest, Logistic Regression
2. **API FastAPI**: Endpoints de scoring avec explications SHAP
3. **Interface Streamlit**: Dashboard interactif
4. **Intégration BCT/CIBCT**: Préparation pour données réelles
5. **Déploiement**: Containerisation Docker

## 📝 Notes Techniques

- **Seed aléatoire**: 42 (reproductibilité)
- **Corrélations**: Âge-revenu, éducation-revenu, PD-classe de risque
- **Réalisme**: Basé sur données INS Tunisie et réglementations BCT
- **Format**: CSV UTF-8, compatible pandas/scikit-learn

---

**Généré le**: 2025-07-01  
**Taille**: 37MB (100k clients × 56 variables)  
**Qualité**: 100/100 ✅
