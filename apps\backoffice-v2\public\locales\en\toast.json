{"dismiss_case": {"success": "The case has been dismissed successfully.", "error": "Error occurred while dismissing the case."}, "flag_case": {"success": "The case has been flagged successfully.", "error": "Error occurred while flagging the case."}, "approve_case": {"success": "The case has been approved successfully.", "error": "Error occurred while approving the case."}, "reject_case": {"success": "The case has been rejected successfully.", "error": "Error occurred while rejecting the case."}, "ask_revision_case": {"success": "<PERSON><PERSON> sent successfully.", "error": "Error occurred while sending email."}, "approve_document": {"success": "The document has been approved successfully.", "error": "Error occurred while approving the document.\n\n{{errorMessage}}"}, "reject_document": {"success": "The document has been rejected successfully.", "error": "Error occurred while rejecting the document.\n\n{{errorMessage}}"}, "ask_revision_document": {"success": "The document has been asked for revision successfully.", "error": "Error occurred while asking for revision of the document.\n\n{{errorMessage}}"}, "revert_revision": {"success": "Asking for document revision has been cancelled successfully.", "error": "Error occurred while reverting the document revision.\n\n{{errorMessage}}"}, "update_document_properties": {"success": "The document has been updated successfully.", "error": "Error occurred while updating the document.\n\n{{errorMessage}}"}, "assign_case": {"success": "The case has been assigned to {{assignee}} successfully.", "error": "Error occurred while assigning the case to {{assignee}}."}, "unassign_case": {"success": "The case has been unassigned successfully.", "error": "Error occurred while unassigning the case."}, "copy_to_clipboard": "Copied to clipboard: {{text}}", "ocr_document_error": "Error occurred while performing OCR on the document.", "validation_error": "❌ Validation error", "event": {"success": "Event sent successfully.", "error": "Error occurred while sending event."}, "case_creation": {"success": "Case created successfully.", "error": "Error occurred while creating a case."}, "assign_alerts": {"success": "The alerts have been assigned to {{assignee}} successfully.", "error": "Error occurred while assigning the alerts to {{assignee}}."}, "unassign_alerts": {"success": "The alerts have been unassigned successfully.", "error": "Error occurred while unassigning the alerts."}, "reject_alerts": {"success": "The alerts have been rejected successfully.", "error": "Error occurred while rejecting the alerts."}, "clear_alerts": {"success": "The alerts have been marked as cleared successfully.", "error": "Error occurred while marking the alerts as cleared."}, "revert_decision_alerts": {"success": "The alerts decision have been reverted successfully.", "error": "Error occurred while reverting the alerts decision."}, "pdf_certificate": {"error": "Failed to open PDF certificate."}, "document_ocr": {"success": "OCR performed successfully.", "empty_extraction": "Unable to extract the document's relevant fields.", "error": "Failed to perform OCR on the document."}, "business_monitoring_off": {"success": "Merchant monitoring has been turned off successfully.", "error": "Error occurred while turning merchant monitoring off."}, "business_monitoring_on": {"success": "Merchant monitoring has been turned on successfully.", "error": "Error occurred while turning merchant monitoring on."}, "business_report_creation": {"success": "Merchant check created successfully.", "error": "Error occurred while creating a merchant check.", "is_example": "Please contact <NAME_EMAIL> for access to this feature."}, "batch_business_report_creation": {"no_file": "No file selected.", "success": "Merchant checks created successfully.", "error": "Error occurred while creating merchant checks.", "is_example": "Please contact <NAME_EMAIL> for access to this feature."}, "business_report_status_update": {"success": "Merchant check status updated successfully.", "unexpected_error": "Something went wrong while updating the status of the report.", "error": "Error occurred while updating merchant check status."}, "note_created": {"success": "Note added successfully.", "error": "Error occurred while adding note."}, "update_details": {"success": "Details updated successfully.", "error": "Error occurred while updating details."}, "ubo_created": {"success": "UBO successfully added", "error": "Error adding UBO"}, "ubo_deleted": {"success": "UBO successfully removed", "error": "Error removing UBO"}, "request_documents": {"success": "Documents requested successfully.", "error": "Error occurred while requesting documents."}, "step_request": {"success": "Step request sent successfully.", "error": "Error occurred while sending step request.\n\n{{errorMessage}}", "error_unknown": "Something went wrong. Please try again later."}, "step_cancel": {"success": "Step request cancelled successfully.", "error": "Error occurred while cancelling step request.\n\n{{errorMessage}}", "error_unknown": "Something went wrong. Please try again later."}, "edit_collection_flow": {"success": "Collection flow's edit mode enabled successfully.", "error": "Error occurred while enabling collection flow's edit mode.", "error_opening_collection_flow": "Error occurred while opening collection flow in new tab."}, "edit_case_state": {"success": "Case state updated successfully.", "error": "Error occurred while updating case state."}, "edit_collection_flow_state_transition": {"error": "Error occurred while transitioning to edit collection flow."}, "kyb_and_ownership_assessment_creation": {"success": "KYB & Ownership assessment created successfully.", "error": "Error occurred while creating KYB & Ownership assessment."}, "assessment_status_update": {"success": "Assessment status updated successfully.", "unexpected_error": "Something went wrong while updating the status of the assessment.", "error": "Error occurred while updating assessment status."}}