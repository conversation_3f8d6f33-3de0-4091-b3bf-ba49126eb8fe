name: Release

on:
  push:
    branches:
      - dev

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  # ci-workflow:
    # uses: ./.github/workflows/ci.yml
  release:
    name: Release
    runs-on: ubuntu-latest
    # needs: [ ci-workflow ]
    # if: ${{ needs.ci-workflow.result=='success' }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Install Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18.12.1

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        id: pnpm-install
        with:
          version: 8
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        shell: bash
        run: pnpm install

      - name: Create Release Pull Request
        uses: changesets/action@v1
        with:
          commit: 'chore: release package(s)'
          publish: pnpm release
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
