# GUIDE D'UTILISATION DES FICHIERS DATASET
## Crédit Scoring Tunisien Multicatégories

---

## 🎯 RÉPONSE À VOS QUESTIONS

### ❓ **"On nettoie pas le dataset ?"**
✅ **O<PERSON>, maintenant c'est fait !** 
- Script de nettoyage créé : `clean_dataset.py`
- Dataset nettoyé disponible : `tunisian_credit_multicategories_100k_clean.csv`
- 47 doublons CIN supprimés
- Formats standardisés
- Score qualité ajouté (99.6/100)

### ❓ **"Est-ce normal d'avoir plusieurs fichiers Excel et TRE/TNR/ERT séparés ?"**
✅ **OUI, c'est RECOMMANDÉ !** Voici pourquoi :

---

## 📁 STRUCTURE DES FICHIERS (33 fichiers CSV)

### 🎯 **FICHIER PRINCIPAL** (1 fichier)
```
📄 tunisian_credit_multicategories_100k_clean.csv
   ├── 99,953 clients (après nettoyage)
   ├── 60 colonnes (score qualité ajouté)
   ├── 3 catégories : TR (75%) + TRE (15%) + ERT (10%)
   └── Usage: ANALYSE GLOBALE et MODÉLISATION PRINCIPALE
```

### 📂 **FICHIERS PAR CATÉGORIE** (3 fichiers)
```
📄 tunisian_credit_tunisien_resident.csv
   ├── 75,022 clients tunisiens résidents
   └── Usage: Modèle spécialisé résidents

📄 tunisian_credit_tunisien_resident_etranger.csv  
   ├── 15,044 clients TRE
   └── Usage: Modèle spécialisé diaspora

📄 tunisian_credit_etranger_resident_tunisie.csv
   ├── 9,934 clients ERT
   └── Usage: Modèle spécialisé étrangers
```

### 🌍 **FICHIERS TRE PAR PAYS** (9 fichiers)
```
📄 tunisian_credit_tre_france.csv (6,794 clients)
📄 tunisian_credit_tre_allemagne.csv (2,235 clients)
📄 tunisian_credit_tre_usa.csv (956 clients)
📄 tunisian_credit_tre_italie.csv (1,847 clients)
📄 tunisian_credit_tre_canada.csv (1,174 clients)
📄 tunisian_credit_tre_arabie_saoudite.csv (731 clients)
📄 tunisian_credit_tre_emirats.csv (598 clients)
📄 tunisian_credit_tre_qatar.csv (428 clients)
📄 tunisian_credit_tre_autres.csv (281 clients)

Usage: Analyse géographique, calibrage par pays
```

### 🏛️ **FICHIERS ERT PAR NATIONALITÉ** (10 fichiers)
```
📄 tunisian_credit_ert_francaise.csv (2,510 clients)
📄 tunisian_credit_ert_algerienne.csv (1,994 clients)
📄 tunisian_credit_ert_libyenne.csv (1,509 clients)
📄 tunisian_credit_ert_italienne.csv (973 clients)
📄 tunisian_credit_ert_allemande.csv (822 clients)
📄 tunisian_credit_ert_marocaine.csv (695 clients)
📄 tunisian_credit_ert_chinoise.csv (460 clients)
📄 tunisian_credit_ert_turque.csv (386 clients)
📄 tunisian_credit_ert_senegalaise.csv (300 clients)
📄 tunisian_credit_ert_autres.csv (285 clients)

Usage: Analyse par communauté, ajustements culturels
```

---

## 🎯 QUAND UTILISER QUEL FICHIER ?

### 🔬 **POUR L'EXPLORATION INITIALE**
```python
# Chargez le fichier principal nettoyé
df = pd.read_csv("output/tunisian_credit_multicategories_100k_clean.csv")

# Analyse globale des 3 catégories
df.groupby('categorie_client')['revenu_mensuel'].describe()
```

### 🤖 **POUR LA MODÉLISATION GLOBALE**
```python
# Modèle unique pour toutes les catégories
X = df.drop(['decision_finale'], axis=1)
y = df['decision_finale']

# La variable 'categorie_client' sera une feature
model = XGBClassifier()
model.fit(X, y)
```

### 🎯 **POUR LA MODÉLISATION SPÉCIALISÉE**
```python
# Modèle spécialisé pour TRE
df_tre = pd.read_csv("output/tunisian_credit_tunisien_resident_etranger.csv")
model_tre = XGBClassifier()
model_tre.fit(df_tre.drop(['decision_finale'], axis=1), df_tre['decision_finale'])

# Modèle spécialisé pour ERT
df_ert = pd.read_csv("output/tunisian_credit_etranger_resident_tunisie.csv")
model_ert = XGBClassifier()
model_ert.fit(df_ert.drop(['decision_finale'], axis=1), df_ert['decision_finale'])
```

### 🌍 **POUR L'ANALYSE GÉOGRAPHIQUE**
```python
# Analyse TRE France vs TRE USA
df_france = pd.read_csv("output/tunisian_credit_tre_france.csv")
df_usa = pd.read_csv("output/tunisian_credit_tre_usa.csv")

# Comparer les profils de risque
print(f"Taux approbation France: {(df_france['decision_finale']=='APPROVE').mean():.1%}")
print(f"Taux approbation USA: {(df_usa['decision_finale']=='APPROVE').mean():.1%}")
```

### 🏦 **POUR L'ANALYSE PAR CLASSE DE RISQUE**
```python
# Analyser les clients C0 (meilleur risque)
df_c0 = pd.read_csv("output/tunisian_credit_classe_C0.csv")
df_c0.groupby('categorie_client').size()
```

---

## ✅ **AVANTAGES DE CETTE STRUCTURE**

### 🚀 **Performance**
- **Chargement rapide** : Seulement les données nécessaires
- **Mémoire optimisée** : 4MB vs 40MB selon le besoin
- **Traitement parallèle** : Plusieurs analyses simultanées

### 🎯 **Flexibilité Analytique**
- **Modèles spécialisés** : Un modèle par catégorie si nécessaire
- **Benchmarking géographique** : Comparer TRE France vs TRE Canada
- **Segmentation fine** : ERT par nationalité pour ajustements

### 🏦 **Conformité Bancaire**
- **Reporting BCT** : Statistiques par catégorie de client
- **Audit trail** : Traçabilité des analyses par segment
- **Validation croisée** : Tests sur sous-populations

### 📊 **Recherche et Développement**
- **A/B Testing** : Tester algorithmes sur segments
- **Feature Engineering** : Variables spécifiques par catégorie
- **Validation externe** : Données réelles par pays/nationalité

---

## 🛠️ **SCRIPTS D'AIDE DISPONIBLES**

### 📋 **Scripts de Génération**
- `generate_dataset_multicategories.py` - Génération complète
- `test_categories.py` - Test des catégories
- `validate_multicategories.py` - Validation étendue

### 🧹 **Scripts de Nettoyage**
- `clean_dataset.py` - Nettoyage et standardisation
- `organize_files.py` - Analyse de la structure

### 📊 **Scripts d'Analyse**
- `validate_dataset.py` - Validation qualité
- Métadonnées JSON disponibles pour chaque export

---

## 💡 **RECOMMANDATIONS PRATIQUES**

### 🎯 **Pour Commencer**
1. **Explorez** avec le fichier principal nettoyé
2. **Validez** les distributions par catégorie
3. **Testez** un modèle global d'abord

### 🚀 **Pour Optimiser**
1. **Comparez** modèle global vs modèles spécialisés
2. **Analysez** les performances par pays/nationalité
3. **Ajustez** les seuils selon les segments

### 🏦 **Pour la Production**
1. **Déployez** le modèle le plus performant
2. **Surveillez** les performances par catégorie
3. **Recalibrez** selon les données réelles BCT/CIBCT

---

## 📞 **SUPPORT**

**Fichiers de référence :**
- `RAPPORT_EXTENSION_MULTICATEGORIES.md` - Rapport détaillé
- `output/dataset_multicategories_metadata.json` - Métadonnées
- `output/rapport_nettoyage.json` - Rapport de nettoyage

**La structure multicatégories est optimale pour votre projet ! 🎉**
