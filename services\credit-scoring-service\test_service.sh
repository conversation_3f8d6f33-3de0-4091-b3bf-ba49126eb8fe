#!/bin/bash

# Test du service de scoring

# 1. Vérifier que le service est en cours d'exécution
echo "Vérification de l'état du service..."
health_response=$(curl -s http://localhost:5000/health)
echo "Réponse du healthcheck: $health_response"

# 2. Tester l'API de scoring avec des données de test
echo "Test de l'API de scoring..."
curl -X POST \
  -H "Content-Type: application/json" \
  -d @test_data.json \
  http://localhost:5000/api/score

echo -e "\nTest terminé!"