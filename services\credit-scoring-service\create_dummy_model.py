import pickle
import numpy as np
from sklearn.ensemble import RandomForestClassifier
import os

# Créer un modèle factice
model = RandomForestClassifier(n_estimators=10, random_state=42)

# Données d'entraînement factices
X = np.random.rand(100, 5)  # 5 features
y = np.random.randint(0, 2, 100)  # Classification binaire

# Entraîner le modèle
model.fit(X, y)

# Créer le dossier models s'il n'existe pas
os.makedirs('models', exist_ok=True)

# Sauvegarder le modèle
with open('models/credit_scoring_model.pkl', 'wb') as f:
    pickle.dump(model, f)

print("Modèle factice créé et sauvegardé dans models/credit_scoring_model.pkl")