import { Blocks } from '@ballerine/blocks';

export const ALL_BLOCKS = [
  'websiteMonitoringBlock',
  'entityInfoBlock',
  'registryInfoBlock',
  'kybRegistryInfoBlock',
  'companySanctionsBlock',
  'individualsUserProvidedBlock',
  'individualsRegistryProvidedBlock',
  'ubosRegistryProvidedBlock',
  'storeInfoBlock',
  'websiteBasicRequirementBlock',
  'bankingDetailsBlock',
  'processingDetailsBlock',
  'mainContactBlock',
  'mainRepresentativeBlock',
  'mapBlock',
  'businessDocumentBlocks',
  'uboDocumentBlocks',
  'directorDocumentBlocks',
  'associatedCompaniesBlock',
  'associatedCompaniesInformationBlock',
  'websiteMonitoringBlocks',
  'documentReviewBlocks',
  'businessInformationBlocks',
  'caseOverviewBlock',
  'customDataBlock',
  'amlWithContainerBlock',
  'merchantScreeningBlock',
  'manageUbosBlock',
  'bankAccountVerificationBlock',
  'commercialCreditCheckBlock',
  'aiSummaryBlock',
  'entityAdditionalInfoBlock',
  'headquartersAddressWithContainerBlock',
  'entityAddressWithContainerBlock',
] as const;

export type TAllBlocks = Record<(typeof ALL_BLOCKS)[number], Blocks>;
